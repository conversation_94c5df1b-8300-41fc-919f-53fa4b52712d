
<!DOCTYPE html>
<html lang="en">

<head>
    <title>Algeria BLS Spain Visa: Welcome to the Official Website Spain Visa Application Centre in Algeria</title>

    <!-- Meta Tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="Algeria BLS Spain Visa">

    <script>

        const storedTheme = localStorage.getItem('theme')

        const getPreferredTheme = () => {
            if (storedTheme) {
                return storedTheme
            }
            return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
        }

        const setTheme = function (theme) {
            if (theme === 'auto' && window.matchMedia('(prefers-color-scheme: dark)').matches) {
                document.documentElement.setAttribute('data-bs-theme', 'dark')
            } else {
                document.documentElement.setAttribute('data-bs-theme', theme)
            }
        }

        setTheme(getPreferredTheme())

        window.addEventListener('DOMContentLoaded', () => {
            var el = document.querySelector('.theme-icon-active');
            if (el != 'undefined' && el != null) {
                const showActiveTheme = theme => {
                    const activeThemeIcon = document.querySelector('.theme-icon-active use')
                    const btnToActive = document.querySelector(`[data-bs-theme-value="${theme}"]`)
                    const svgOfActiveBtn = btnToActive.querySelector('.mode-switch use').getAttribute('href')

                    document.querySelectorAll('[data-bs-theme-value]').forEach(element => {
                        element.classList.remove('active')
                    })

                    btnToActive.classList.add('active')
                    activeThemeIcon.setAttribute('href', svgOfActiveBtn)
                }

                window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', () => {
                    if (storedTheme !== 'light' || storedTheme !== 'dark') {
                        setTheme(getPreferredTheme())
                    }
                })

                showActiveTheme(getPreferredTheme())

                document.querySelectorAll('[data-bs-theme-value]')
                    .forEach(toggle => {
                        toggle.addEventListener('click', () => {
                            const theme = toggle.getAttribute('data-bs-theme-value')
                            localStorage.setItem('theme', theme)
                            setTheme(theme)
                            showActiveTheme(theme)
                        })
                    })

            }
        })

    </script>

    <!-- Favicon -->
    <link rel="shortcut icon" href="/assets/images/favicon.png">

    <!-- Google Font -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=DM+Sans:wght@400;500;700&family=Poppins:wght@400;500;700&display=swap">

    <!-- Plugins CSS -->
    <link rel="stylesheet" type="text/css" href="/assets/vendor/font-awesome/css/all.min.css?v=AbA177XfpSnFEvgpYu1jMygiLabzPCJCRIBtR5jGc0k">
    <link rel="stylesheet" type="text/css" href="/assets/vendor/bootstrap-icons/bootstrap-icons.css?v=4RctOgogjPAdwGbwq-rxfwAmSpZhWaafcZR9btzUk18">
    <link rel="stylesheet" type="text/css" href="/assets/vendor/tiny-slider/tiny-slider.css?v=1CXVlacnwYLmg9X2AhCKvYcSgR53GWiU3z4qZJDrb68">
    <link rel="stylesheet" type="text/css" href="/assets/vendor/glightbox/css/glightbox.css?v=yMoCTayb6wccTz561YN_R0qkP756JifYXnA40MUtR-k">
    <link rel="stylesheet" type="text/css" href="/assets/vendor/flatpickr/css/flatpickr.min.css?v=RXPAyxHVyMLxb0TYCM2OW5R4GWkcDe02jdYgyZp41OU">
    <link rel="stylesheet" type="text/css" href="/assets/vendor/choices/css/choices.min.css?v=IhTRSpOAAOl37YqrDOlNjxD6S4lzZ_n2WqKtc03i0mE">

    <!-- Theme CSS -->
    <link rel="stylesheet" type="text/css" href="/assets/vendor/bootstrap/dist/css/bootstrap.min.css?v=YLGeXaapI0_5IgZopewRJcFXomhRMlYYjugPLSyNjTY">
    <link rel="stylesheet" type="text/css" href="/assets/css/style.css?v=CJhTkVmGHvWZRE3zX8Z_q23nYkeJQHqx-4TqNQdi9Hg">

    <link rel="stylesheet" type="text/css" href="/assets/css/color.css?v=biNDUrJj74VlRbGB7tvoVV8aOPeiVsRt7L_VDdhggXQ" />

    <link rel="stylesheet" type="text/css" href="/css/site.css?v=ekmtryaKttePPamW3CxEz99-N5ruMwA9JAkOw57RgzI" />

    
    <link rel="stylesheet" type="text/css" href="/assets/vendor/kendo/css/kendo.common.min.css">
    <link rel="stylesheet" type="text/css" href="/assets/vendor/kendo/css/kendo.silver.min.css">

    <style>
    </style>
    <script src="/assets/vendor/jquery/dist/jquery.min.js"></script>
    <script src="/assets/vendor/jquery-validation/dist/jquery.validate.min.js"></script>
    <script src="/assets/vendor/jquery-ajax-unobtrusive/jquery-ajax-unobtrusive.js"></script>
    <script src="/assets/vendor/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min.js"></script>
    <script type="text/javascript" src="https://5f2749aa43d4.eu-central-1.sdk.awswaf.com/5f2749aa43d4/4bc7b7b893fe/challenge.js"  defer></script>
    <script>
        $.ajaxSetup({
            beforeSend: function (xhr, options) {
                if (options.type.toUpperCase() == "POST") {
                    xhr.setRequestHeader("RequestVerificationToken", $('input:hidden[name="__RequestVerificationToken"]').val());
                }

            }
        });
    </script>
</head>
<body>
    <div class="preloader">
        <div class="preloader-item">
            <div class="spinner-grow text-primary"></div>
        </div>
    </div>
    <div id="global-overlay" class="global-overlay">
        <div class="global-overlay-loader">
        </div>
    </div>
    <header class="navbar-light header-sticky">
        <!-- Nav START -->
        <nav class="navbar navbar-expand-xl z-index-9 navbar-divider">
            <div class="container">
                <!-- Logo START -->
                <a class="navbar-brand" href="/DZA/home/<USER>">
                    <img class="light-mode-item navbar-brand-item" src="/assets/images/logo.png" alt="BLS Logo" title="BLS Logo">
                    <img class="dark-mode-item navbar-brand-item" src="/assets/images/logo.png" alt="logo">
                </a>
                <!-- Logo END -->
                <!-- Responsive navbar toggler -->
                <button class="navbar-toggler ms-auto" type="button" data-bs-toggle="collapse" data-bs-target="#navbarCollapse" aria-controls="navbarCollapse" aria-expanded="false" aria-label="Toggle navigation">
                    <i class="bi bi-search fs-4"> </i>
                </button>

                <!-- Responsive navbar toggler -->
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarCollapse2" aria-controls="navbarCollapse2" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-animation">
                        <span></span>
                        <span></span>
                        <span></span>
                    </span>
                </button>

                <!-- Main navbar START -->
                <div class="navbar-collapse collapse" id="navbarCollapse">
                    <div class="col-md-9">
                        <div class="nav my-3 my-xl-0 px-4 flex-nowrap align-items-center">
                            <div class="nav-item w-100">
                                <div class="align-items-center position-relative">
                                    <div class="align-items-center text-primary" style="font-size:30px;font-weight:700;">Apply for VISA to Spain In Algeria</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="align-items-center position-relative">
                            <small class="text-secondary mb-1">Follow us on</small>
                            <ul class="list-inline mb-3 mt-0">
                                <li class="list-inline-item"> <a class="btn btn-sm px-2 bg-facebook mb-0" href="https://www.facebook.com/blsinternationalservices/" target="_blank" rel="noopener noreferrer"><i class="fab fa-fw fa-facebook-f"></i></a> </li>
                                <li class="list-inline-item"> <a class="btn btn-sm shadow px-2 bg-instagram mb-0" href="https://www.instagram.com/blsinternationalservicesltd/" target="_blank" rel="noopener noreferrer"><i class="fab fa-fw fa-instagram"></i></a> </li>
                                <li class="list-inline-item"> <a class="btn btn-sm shadow px-2 bg-twitter mb-0" href="https://twitter.com/blsintlservices" target="_blank" rel="noopener noreferrer"><i class="fab fa-fw fa-twitter"></i></a> </li>
                                <li class="list-inline-item"> <a class="btn btn-sm shadow px-2 bg-linkedin mb-0" href="https://www.linkedin.com/company/bls-international-services-ltd/?originalSubdomain=in" target="_blank" rel="noopener noreferrer"><i class="fab fa-fw fa-linkedin-in"></i></a> </li>
                            </ul>
                        </div>
                    </div>
                </div>

                <!-- Main navbar END -->
                <!-- Profile and notification START -->
                <ul class="nav flex-row align-items-center list-unstyled ms-xl-auto">
                    <li class="dropdown nav-item">
                        <a class="nav-link small pb-2" href="#" role="button" id="languageDropdown" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <img class="w-30px me-2" src="/assets/images/flags/en-US.svg" alt=""><small>English</small>
                        </a>
                        <ul class="dropdown-menu dropdown-animation dropdown-menu-end min-w-auto" aria-labelledby="languageDropdown">
                            <li> <a class="dropdown-item me-4" href="javascript:OnLanguageChange('en-US');"><img class="fa-fw me-2" src="/assets/images/flags/en-US.svg" alt="">English</a> </li>
                            <li> <a class="dropdown-item me-4" href="javascript:OnLanguageChange('es-ES');"><img class="fa-fw me-2" src="/assets/images/flags/es-ES.svg" alt="">Español</a> </li>
                            <li> <a class="dropdown-item me-4" href="javascript:OnLanguageChange('fr-FR');"><img class="fa-fw me-2" src="/assets/images/flags/fr-FR.svg" alt="">Français</a> </li>
                            <li> <a class="dropdown-item me-4" href="javascript:OnLanguageChange('ar-DZ');"><img class="fa-fw me-2" src="/assets/images/flags/ar-DZ.svg" alt="">عربي</a> </li>
                        </ul>
                    </li>
                        <li class="nav-item ms-3 dropdown">
                            <!-- Avatar -->
                            <a class="avatar avatar-sm p-0 mt-0" href="#" id="profileDropdown" role="button" data-bs-auto-close="outside" data-bs-display="static" data-bs-toggle="dropdown" aria-expanded="false">
                                    <img onerror="OnPhotoError(this);" class="avatar-img rounded-2" src="/assets/images/avatar/01.jpg" alt="Profile">
                            </a>

                            <!-- Profile dropdown START -->
                            <ul class="dropdown-menu dropdown-animation dropdown-menu-end shadow pt-3" aria-labelledby="profileDropdown">
                                <!-- Profile info -->
                                <li class="px-3 mb-3">
                                    <div class="d-flex align-items-center">
                                        <!-- Avatar -->
                                        <div class="avatar me-3">
                                                <img onerror="OnPhotoError(this);" class="avatar-img rounded-circle shadow" src="/assets/images/avatar/01.jpg" alt="Profile">

                                        </div>
                                        <div>
                                            <a class="h6 mt-2 mt-sm-0" href="#">ISLAM TOUATI</a>
                                            <p class="small m-0"><EMAIL></p>
                                        </div>
                                    </div>
                                </li>

                                <!-- Links -->
                                <li> <hr class="dropdown-divider"></li>
                                <li></li>
                                <li><a class="dropdown-item" href="/DZA/account/DeleteUser"><i class="fa fa-trash fa-fw me-2"></i>Delete Account</a></li>
                                <li><a class="dropdown-item" href="/DZA/account/ChangePassword"><i class="fa fa-key fa-fw me-2"></i>Change Password</a></li>
                                <li><a class="dropdown-item bg-danger-soft-hover" href="javascript:OnLogout();"><i class="fa fa-power-off me-2"></i>Logout</a></li>
                                <li> <hr class="dropdown-divider"></li>

                                <!-- Dark mode options START -->
                                <!-- Dark mode options END-->
                            </ul>
                            <!-- Profile dropdown END -->
                        </li>
                    <!-- Profile dropdown START -->
                    <!-- Profile dropdown END -->
                </ul>
                <!-- Profile and notification END -->
            </div>
        </nav>
        <!-- Nav END -->
        <!--Main menu link START -->
        <nav class="navbar navbar-expand-xl navbar-divider">
            <div class="container px-0">
                <!-- Main navbar START -->
                <div class="navbar-collapse w-100 collapse" id="navbarCollapse2">
                    <ul class="navbar-nav nav-active-line navbar-nav-scroll">
                        <li class="nav-item"> <a class="nav-link home-active" href="https://algeria.blsspainvisa.com"><i class="fa fa-home"></i></a>	</li>

                        <!-- Nav item -->
                        <li class="nav-item"> <a class="nav-link new-app-active" href="/DZA/appointment/newappointment">Book New Appointment</a>	</li>
                        <!-- Nav item -->

                       <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle my-app-active" href="#" id="pageMenu" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">Manage Appointments</a>
                            <ul class="dropdown-menu" aria-labelledby="pageMenu">

                                <li>
                                    <a class="dropdown-item" href="/DZA/appointmentdata/BLSCancelAppointment">
                                        <i class="text-danger fa-regular fa-calendar-xmark me-2"></i>Cancel Appointment
                                    </a>
                                </li>

                                <li>
                                    <a class="dropdown-item" href="/DZA/appointmentdata/BLSReprintAppointmentLetter">
                                        <i class="text-info fa-solid fa-print me-2"></i>Re-Print Appointment Letter
                                    </a>
                                </li>
                                <li>
                                    <a class="dropdown-item" href="/DZA/appointmentdata/myappointments">
                                        <i class="text-info fa-solid fa-users me-2"></i>Manage Applicants
                                    </a>
                                </li>
                            </ul>
                        </li>

                        <li class="nav-item"><a class="nav-link doorstep-active text-primary fw-bolder" target="_blank" href="https://bls.schengen.europ-assistance.com/en?ipc=blsalgeria&utm_source=blsalgeria&utm_medium=affiliates">Travel Insurance</a></li>


                    </ul>

                </div>
                <!-- Main navbar END -->
            </div>
        </nav>
        <!--Main menu link END -->
    </header>
    <!-- **************** MAIN CONTENT START **************** -->
    <main>
        
<style>
    .validation-summary ul {
        list-style-type: none;
    }
</style> 
<div class="row">
    <div class="col-12 row p-4 justify-content-center">
        <div class="col-12 justify-content-center text-center pb-2">
                <h5>Book New Appointment - Visa Type Selection</h5>

        </div>
    </div>
    <div class="col-12">
        <style>
.bls-card{
    border:1px solid gray;
}
.form-check-input {
    margin-top: 0.2em !important;
    margin-left: 0px !important;
    margin-right: 5px !important;
}
    .nHvvc{z-index:1;}.RSvld{z-index:1;}.IEpTo{z-index:1;}.moREabx{z-index:1;}.aTev{z-index:1;}.SpxUax{z-index:1;}.dVTETI{z-index:1;}.Tbmx{z-index:1;}.Fclm{z-index:1;}.IHoUa{z-index:1;}.UHVGm{z-index:1;}.lGpv{z-index:1;}.QnvnHn{z-index:1;}.EaQvaRy{z-index:1;}.dalpdI{z-index:1;}.EVSU{z-index:1;}.RRFdPTo{display:none !important;z-index:1;}.Rebpwpa{display:none !important;z-index:1;}.yHedFV{display:none !important;z-index:1;}.wanV{display:none !important;z-index:1;}.IwvPma{display:none !important;z-index:1;}.lenmTlP{display:none !important;z-index:1;}.dVIpcUw{display:block !important;z-index:1;}.PUnR{display:block !important;z-index:1;}.ecxTGl{display:block !important;z-index:1;}.QocvF{display:block !important;z-index:1;}.xlvmURI{display:block !important;z-index:1;}.oaFGF{display:block !important;z-index:1;}.VeVnPUE{display:block !important;z-index:1;}.lQaE{display:block !important;z-index:1;}.SbRTnP{display:block !important;z-index:1;}.xlRxa{display:block !important;z-index:1;}.nRcT{display:block !important;}.bvvFo{display:block !important;}.HHlwaGm{display:block !important;}.mRnxd{display:block !important;}.SnoQp{display:block !important;}.nnQne{display:block !important;}.SPye{display:block !important;}.xxbGc{display:block !important;}.aeem{display:block !important;}.lnVpUG{display:block !important;}.PcFlR{display:block !important;}.pSUnUP{display:block !important;}.ScoRVb{display:block !important;}.QloFlm{display:block !important;}.xoRe{display:none !important;}.pPbSm{display:none !important;}.EmldPm{display:none !important;}.PQcVe{display:none !important;}.TVFEQ{display:none !important;}.aFSo{display:none !important;}.GwQxe{display:none !important;}.awEo{display:none !important;}.vFbEpP{display:none !important;}.SUydxm{display:none !important;}.QlnSm{display:none !important;}.HURy{display:none !important;}.xUFHc{display:none !important;}.FavEVx{display:none !important;}.Gmba{display:none !important;}.SxVye{display:none !important;}.TIFnm{display:none !important;}.oQcmdly{display:block !important;}.VESS{display:block !important;}.GPen{display:block !important;}.lepQVb{display:block !important;}.RFVUmd{display:block !important;}.nooIPSc{display:block !important;}.vxwHUy{display:block !important;}.awTn{display:block !important;}.neEER{display:block !important;}.HoFE{display:block !important;}.IwaF{display:block !important;}.nbwvwyP{display:block !important;}.pndcGEy{display:block !important;}.VSEc{display:block !important;}.SIlQbRo{display:none !important;}.mTTpPp{display:none !important;}.SQQvG{display:none !important;}.yTEH{display:none !important;}.acPE{display:none !important;}.xbyeI{display:none !important;}.HwUE{display:none !important;}.bllnRe{display:none !important;}.cbQIdF{display:none !important;}.QGcaQ{display:none !important;}.VHwvn{display:none !important;}.ePHd{display:none !important;}.xcEwI{display:none !important;}.plSyG{display:none !important;}.HPUURU{display:none !important;}.EvdoHv{display:none !important;}.QaGVRw{display:none !important;}.wmaaUbn{display:none !important;}.RUPoeQ{display:none !important;}.ompRdE{display:none !important;}.oPSbVFp{display:none !important;}
</style>
<script>
       
    var applicantId = 0;
    var catItem = null;
    var locationDataItem = null;
    var familyModalClose = false;
    function onDrpOpen(e) {
        var id = e.sender.element[0].id;
        var item = rspData.find(x => x.Id === id);
        rspData = RemoveItem(id);
        if (item !== null && item !== undefined) {
            item.Start = new Date($.now());
            item.Selected = false;
        }
        else {
            item = { Id: id, Start: new Date($.now()), End: null, Total: null, Selected: false };
        }
        rspData.push(item);
    }
    function onselect1(id) {
        $('.bls-applicant').removeClass('alert-primary');
        $('.bls-applicant').addClass('alert-light');
        $("#app-"+id).removeClass('alert-light');
        $("#app-"+id).addClass('alert-primary');
        $("input[name='Applicant']").prop('checked', false);
        $("#rdo-" + id).prop('checked', true);
    }
     function onDrpSelect(e) {
         var id = e.sender.element[0].id;
         var item = rspData.find(x => x.Id === id);
         rspData = RemoveItem(id);
         if (item !== null && item !== undefined) {
             item.Selected = true;
             rspData.push(item);
         }
     };
     function RemoveItem(id) {
             return rspData.filter(function (e) {
                 return e.Id !== id;
             });
     }
     function onDrpClose(e) {
         var id = e.sender.element[0].id;
         var item = rspData.find(x => x.Id === id);
         rspData = RemoveItem(id);
         if (item !== null && item !== undefined) {
             if (item.Selected) {
                 item.End = new Date($.now());
                 item.Total = item.End - item.Start;
                 item.Selected = true;
             }
             rspData.push(item);
         }
     }
    function OnSubmitVisaType() {
        ShowLoader();
        var submittedData={UPvyH : $("#UPvyH").val(),
Tamae : $("#Tamae").val(),
oPaPnpp : $("#oPaPnpp").val(),
SpaUdG : $("#SpaUdG").val(),
EGHHIRo : $("#EGHHIRo").val(),
VImxxn : $("#VImxxn").val(),
GGxlx : $("#GGxlx").val(),
IyeRyI : $("#IyeRyI").val(),
UbPR : $("#UbPR").val(),
mSGExU : $("#mSGExU").val(),
UanTRc : $("#UanTRc").val(),
ddTe : $("#ddTe").val(),
cPEPnUS : $("#cPEPnUS").val(),
eQwIvv : $("#eQwIvv").val(),
vlnRQQo : $("#vlnRQQo").val(),
TEURn : $("#TEURn").val(),
pcRFne : $("#pcRFne").val(),
GUVbcan : $("#GUVbcan").val(),
Hylb : $("#Hylb").val(),
vvdT : $("#vvdT").val(),
xGSGP : $("#anxGSGP").val(),
mpvnx : $("#anmpvnx").val(),
mPIae : $("#anmPIae").val(),
wHnoFn : $("#anwHnoFn").val(),
FbpTQcI : $("#anFbpTQcI").val(),
};;
        $("#ResponseData").val(JSON.stringify(submittedData));
        document.getElementById('visatypeform').submit();
        return false;
        // grecaptcha.ready(function () {
        //     grecaptcha.execute('6Ldkm4UrAAAAADAFSmvv1au-CN9RDlbjV82ws_Hb', {action: 'VisaType'}).then(function (token) {
        //         document.getElementById("ReCaptchaToken").value = token;
        //         document.getElementById('visatypeform').submit();
        //         return false;
        //     });
        // });
        // return false;
    }
    function OnAppointmentForChange(e, id) {
        applicantId = id;
        $("#members"+id).hide();
        $("#an"+id).data("kendoDropDownList").value("");
        $("#an"+id).data("kendoDropDownList").value(null);
        if (e !== null && e.target.id == "family" + id) {
        $("#members" + id).show();
        $('#AppointmentFor').val('Family');
         $('#familyDisclaimer').modal('show');
        }
        else { 
            $('#AppointmentFor').val('Individual');
        }
    }
    function OnFamilyReject(){
        var appointmentFor = document.getElementById("self" + applicantId);
        appointmentFor.checked = true;
        OnAppointmentForChange(null, applicantId);
        familyModalClose = true;
        $('#familyDisclaimer').modal('hide');
    }
    function OnFamilyAccept() {
        familyModalClose = true;
        $('#familyDisclaimer').modal('hide');
    }
    function OnPlReject(){
        $("#"+catItem).data("kendoDropDownList").value(null);
        $("#PremiumTypeModel").modal('hide');
    }    
    var addressModalClose = false;
    var applicantsNoFilterData = [];
    var visaTypeFilterData = [];
    var visasubIdFilterData = [];
    var locationFilterData = [];
    var applicantId = 0;
    var rspData = [];
var lPHvnm=[{"Id":"31668","Name":"Oran","Code":"ORAN","VisaTypeIds":"[\"9693\",\"9696\",\"9697\"]","VisaSubTypeIds":"[\"1307\",\"1308\",\"1309\",\"1310\",\"1311\",\"1317\"]","MissionId":"7b831dab-5bed-4f9e-9e13-a301dfce2d77"},{"Id":"31669","Name":"Algiers","Code":"ALGIERS","VisaTypeIds":"[\"9694\",\"9695\",\"9696\",\"9697\"]","VisaSubTypeIds":"[\"1306\",\"1312\",\"1313\",\"1314\",\"1315\",\"1316\"]","MissionId":"ec336bcf-29fe-4d76-90f1-a7ae2d74d78b"}];var oIIGdcP=[{"Id":"81668","Name":"Oran","Code":"ORAN","VisaTypeIds":"[\"9693\",\"9696\",\"9697\"]","VisaSubTypeIds":"[\"1307\",\"1308\",\"1309\",\"1310\",\"1311\",\"1317\"]","MissionId":"7b831dab-5bed-4f9e-9e13-a301dfce2d77"},{"Id":"81669","Name":"Algiers","Code":"ALGIERS","VisaTypeIds":"[\"9694\",\"9695\",\"9696\",\"9697\"]","VisaSubTypeIds":"[\"1306\",\"1312\",\"1313\",\"1314\",\"1315\",\"1316\"]","MissionId":"ec336bcf-29fe-4d76-90f1-a7ae2d74d78b"}];var HydFdoF=[{"Id":"41668","Name":"Oran","Code":"ORAN","VisaTypeIds":"[\"9693\",\"9696\",\"9697\"]","VisaSubTypeIds":"[\"1307\",\"1308\",\"1309\",\"1310\",\"1311\",\"1317\"]","MissionId":"7b831dab-5bed-4f9e-9e13-a301dfce2d77"},{"Id":"41669","Name":"Algiers","Code":"ALGIERS","VisaTypeIds":"[\"9694\",\"9695\",\"9696\",\"9697\"]","VisaSubTypeIds":"[\"1306\",\"1312\",\"1313\",\"1314\",\"1315\",\"1316\"]","MissionId":"ec336bcf-29fe-4d76-90f1-a7ae2d74d78b"}];    var visasubIdData =[{"Id":"1304","Name":"Schengen Visa","Value":"9694","Code":"WEB_BLS","VisaSubTypeCode":"SCHENGEN_VISA"},{"Id":"1305","Name":"Tourism","Value":"9694","Code":"WEB_BLS","VisaSubTypeCode":"TOURISM"},{"Id":"1306","Name":"Schengen visa ( Estonia)","Value":"9695","Code":"WEB_BLS","VisaSubTypeCode":"SCHENGEN_VISA_ESTONIA"},{"Id":"1307","Name":"Oran 1","Value":"9696","Code":"WEB_BLS","VisaSubTypeCode":"SCHENGEN_VISA_ORAN_ONE"},{"Id":"1308","Name":"Oran 2","Value":"9697","Code":"WEB_BLS","VisaSubTypeCode":"SCHENGEN_VISA_ORAN_TWO"},{"Id":"1309","Name":"Oran 3","Value":"9697","Code":"WEB_BLS","VisaSubTypeCode":"SCHENGEN_VISA_ORAN_THREE"},{"Id":"1310","Name":"Family reunification visa","Value":"9693","Code":"WEB_BLS","VisaSubTypeCode":"FAMILY_REUNIFICATION"},{"Id":"1311","Name":"Oran 4","Value":"9697","Code":"WEB_BLS","VisaSubTypeCode":"SCHENGEN_VISA_ORAN_FOUR"},{"Id":"1312","Name":"ALG 1","Value":"9696","Code":"WEB_BLS","VisaSubTypeCode":"SCHENGEN_VISA_ALG_ONE"},{"Id":"1313","Name":"ALG 2","Value":"9697","Code":"WEB_BLS","VisaSubTypeCode":"SCHENGEN_VISA_ALG_TWO"},{"Id":"1314","Name":"ALG 3","Value":"9697","Code":"WEB_BLS","VisaSubTypeCode":"SCHENGEN_VISA_ALG_THREE"},{"Id":"1315","Name":"ALG 4","Value":"9697","Code":"WEB_BLS","VisaSubTypeCode":"SCHENGEN_VISA_ALG_FOUR"},{"Id":"1316","Name":"FAMILY GROUP","Value":"9694","Code":"WEB_BLS","VisaSubTypeCode":"FAMILY_GROUP"},{"Id":"1317","Name":"Study visa","Value":"9693","Code":"WEB_BLS","VisaSubTypeCode":"STUDY_VISA"}];
    var redirect = '';
    var visaIdData =[{"Id":"9693","Name":"National Visa","VisaTypeCode":"NATIONAL_VISA","AppointmentSource":null},{"Id":"9694","Name":"Schengen Visa","VisaTypeCode":"SCHENGEN_VISA","AppointmentSource":null},{"Id":"9695","Name":"Schengen visa ( Estonia)","VisaTypeCode":"SCHENGEN_VISA_ESTONIA","AppointmentSource":"WEB_BLS"},{"Id":"9696","Name":"First application / première demande","VisaTypeCode":"SCHENGEN_VISA","AppointmentSource":"WEB_BLS"},{"Id":"9697","Name":"Visa renewal / renouvellement de visa","VisaTypeCode":"SCHENGEN_VISA_EXISTING","AppointmentSource":"WEB_BLS"}];
    var applicantsNoData=[{"Id":"4343","Name":"2 Members","Value":"4343"},{"Id":"4344","Name":"3 Members","Value":"4344"},{"Id":"4345","Name":"4 Members","Value":"4345"},{"Id":"4346","Name":"5 Members","Value":"4346"},{"Id":"4347","Name":"6 Members","Value":"4347"},{"Id":"4348","Name":"7 Members","Value":"4348"},{"Id":"4349","Name":"8 Members","Value":"4349"}];
    var categoryData=[{"Id":"6186","Name":"Normal","Code":"CATEGORY_NORMAL"},{"Id":"6187","Name":"Premium","Code":"CATEGORY_PREMIUM"},{"Id":"6188","Name":"Prime Time","Code":"PRIME_TIME"}];
</script>

<div id="div-main" class="row pb-3 pl-5 pr-5">
        <div class="d-none d-sm-block col-md-2">
        </div>
        <div class="shadow rounded-3 p-3 col-md-3 d-sm-12">
            <div class="text-center col-12">
                <a href="index.html">
                    <img class="h-50px mb-2" src="/assets/images/logo.png" alt="logo">
                </a>
            </div>
            <form id="visatypeform" method="post" action="/DZA/Appointment/VisaType">
                <div class="validation-summary text-danger mb-3 validation-summary-valid" data-valmsg-summary="true"><ul><li style="display:none"></li>
</ul></div>
                <div class="pt-1">
<div class="mb-3 Fclm QnvnHn IHoUa Tbmx aTev UHVGm boUQlPp dalpdI IclaaHE myTTSS EVSU eSaEwSI EaQvaRy nHvvc moREabx dVTETI lGpv RSvld QEnSQ IEpTo SpxUax mt-5">
	                                <label class="form-label" for="VImxxn">Location<span class="required">*</span></label>
	                                <input id="VImxxn" name="VImxxn" style="width:100%">
                                </div><div class="mb-3 IHoUa QEnSQ moREabx dVTETI nHvvc EVSU Fclm IEpTo EaQvaRy RSvld aTev SpxUax Tbmx yRaw SIlQbRo QnvnHn lGpv UHVGm dalpdI mt-5">
	                                <label class="form-label" for="GGxlx">Location<span class="required">*</span></label>
	                                <input id="GGxlx" name="GGxlx" style="width:100%">
                                </div><div class="mb-3 dVTETI IHoUa QnvnHn UHVGm RSvld nHvvc moREabx SIlQbRo Tbmx GdQbG Fclm aTev IEpTo SpxUax lGpv mt-5">
	                                <label class="form-label" for="IyeRyI">Location<span class="required">*</span></label>
	                                <input id="IyeRyI" name="IyeRyI" style="width:100%">
                                </div><div class="mb-3 bvEc EaQvaRy SpxUax wFlGpn RSvld TRcbdQ moREabx vvVQH oQQpe nHvvc Fdxv Tvnn IEpTo mImEmnw STIyUm mwTU TaPvxbm EVSU Fclm UHVGm IHoUa pRVwwUP mxPwTV aTev SRVa RVPlvpv IdSPym IclaaHE wdRxSUy wwHemo eTSx QEnSQ lFwwm lGpv dalpdI oFEPE dVTETI QnvnHn Tbmx GcoGm nGFPew eSaEwSI boUQlPp nQEceUU yvlowVS mt-5">
	                                <label class="form-label" for="UbPR">Location<span class="required">*</span></label>
	                                <input id="UbPR" name="UbPR" style="width:100%">
                                </div><div class="mb-3 lGpv aTev IEpTo clFyHHp IHoUa eSaEwSI QEnSQ SpxUax nHvvc Tbmx Fclm QnvnHn dVTETI dalpdI RSvld EVSU moREabx EaQvaRy UHVGm mt-5">
	                                <label class="form-label" for="mSGExU">Location<span class="required">*</span></label>
	                                <input id="mSGExU" name="mSGExU" style="width:100%">
                                </div><div class="mb-3 pRVwwUP IclaaHE eTSx GHmIb EVSU IEpTo TEaVl bvEc wIcRx RvwPGxU dalpdI eSaEwSI wFlGpn adEFxQ oFEPE yaVU vvVQH TRcbdQ nHvvc QEnSQ IHoUa lGpv dVTETI RVPlvpv oQQpe moREabx TaPvxbm wQccUll mxPwTV ynPRwl nGoe boUQlPp Tbmx IdSPym nGFPew STIyUm bdlyRpF RVeGbSy wdRxSUy GcoGm yvlowVS SpxUax SRVa xwnm QUwbaFE EaQvaRy Fclm Fdxv aTev mwTU wwHemo RSvld UHVGm Tvnn nQEceUU QnvnHn VemeaSU mImEmnw mt-3">
	                                <label class="form-label" for="UanTRc">Visa Type<span class="required">*</span></label>
	                                <input id="UanTRc" name="UanTRc" style="width:100%">
                                </div><div class="mb-3 QEnSQ wFlGpn QnvnHn oQQpe dalpdI nGFPew Fclm Tvnn vvVQH aTev TaPvxbm eTSx IdSPym TRcbdQ EVSU wdRxSUy Tbmx nQEceUU moREabx lGpv EaQvaRy dVTETI IHoUa UHVGm nHvvc vFFby yvlowVS SpxUax IEpTo oFEPE eSaEwSI IclaaHE boUQlPp pRVwwUP RSvld mt-3">
	                                <label class="form-label" for="ddTe">Visa Type<span class="required">*</span></label>
	                                <input id="ddTe" name="ddTe" style="width:100%">
                                </div><div class="mb-3 IclaaHE lGpv EaQvaRy GcoGm wFlGpn oQQpe eSaEwSI RSvld Fclm EVSU QEnSQ SpxUax dVTETI Ubmc TaPvxbm TRcbdQ aTev nHvvc vvVQH IHoUa nQEceUU Tbmx bvEc mwTU dalpdI IdSPym boUQlPp pRVwwUP UHVGm IEpTo eTSx yvlowVS Fdxv oFEPE QnvnHn moREabx wdRxSUy nGFPew mImEmnw Tvnn mt-3">
	                                <label class="form-label" for="cPEPnUS">Visa Type<span class="required">*</span></label>
	                                <input id="cPEPnUS" name="cPEPnUS" style="width:100%">
                                </div><div class="mb-3 Tvnn lGpv dVTETI mImEmnw FedFbG IEpTo IclaaHE oFEPE eTSx wwHemo nGoe TRcbdQ mwTU IHoUa aTev vvVQH nHvvc wFlGpn RVPlvpv moREabx TaPvxbm oQQpe Fclm Tbmx RSvld adEFxQ boUQlPp SRVa eSaEwSI UHVGm QEnSQ GcoGm yvlowVS IdSPym pRVwwUP EaQvaRy wdRxSUy dalpdI nGFPew STIyUm QnvnHn mxPwTV Fdxv RvwPGxU SpxUax bvEc nQEceUU yaVU EVSU mt-3">
	                                <label class="form-label" for="eQwIvv">Visa Type<span class="required">*</span></label>
	                                <input id="eQwIvv" name="eQwIvv" style="width:100%">
                                </div><div class="mb-3 IclaaHE IHoUa dalpdI UHVGm eSaEwSI lGpv RSvld Tbmx aTev SpxUax QnvnHn QEnSQ EaQvaRy SIlQbRo nHvvc EVSU dVTETI moREabx Fclm IEpTo dPwQyd mt-3">
	                                <label class="form-label" for="vlnRQQo">Visa Type<span class="required">*</span></label>
	                                <input id="vlnRQQo" name="vlnRQQo" style="width:100%">
                                </div><div class="mb-3 QEnSQ wdRxSUy SIlQbRo lGpv dVTETI dalpdI damd EVSU SpxUax RSvld IclaaHE eSaEwSI QnvnHn Tbmx moREabx EaQvaRy Fclm UHVGm nQEceUU eTSx IHoUa nGFPew IEpTo TRcbdQ TaPvxbm wFlGpn boUQlPp nHvvc aTev mt-6">
	                                <label class="form-label" for="TEURn">Visa Sub Type<span class="required">*</span></label>
	                                <input id="TEURn" name="TEURn" style="width:100%">
                                </div><div class="mb-3 TaPvxbm nQEceUU eTSx pRVwwUP Fclm IHoUa QEnSQ yvlowVS bvEc oFEPE SpxUax nHvvc yaVU IEpTo adEFxQ IdSPym mxPwTV RSvld mImEmnw aTev RVeGbSy GcoGm Fdxv RVPlvpv bdlyRpF dVTETI dalpdI GHmIb IdaFwym TRcbdQ eSaEwSI boUQlPp UHVGm nGFPew EVSU Tvnn wdRxSUy EaQvaRy STIyUm wFlGpn RvwPGxU wwHemo SRVa vvVQH mwTU Tbmx nGoe lGpv moREabx IclaaHE QnvnHn oQQpe mt-6">
	                                <label class="form-label" for="pcRFne">Visa Sub Type<span class="required">*</span></label>
	                                <input id="pcRFne" name="pcRFne" style="width:100%">
                                </div><div class="mb-3 moREabx mxPwTV IEpTo adEFxQ EVSU lGpv mImEmnw QnvnHn nGoe dalpdI oQQpe boUQlPp wFlGpn vvVQH Tbmx RVPlvpv nQEceUU nHvvc dVTETI IclaaHE eTSx eSaEwSI yaVU nGFPew mwTU pRVwwUP wdRxSUy EaQvaRy bvEc wwHemo QEnSQ RSvld Fdxv IdSPym Tvnn Fclm GcoGm SRVa IHoUa yvlowVS oFEPE TaPvxbm SpxUax TRcbdQ STIyUm aTev nGbp UHVGm mt-6">
	                                <label class="form-label" for="GUVbcan">Visa Sub Type<span class="required">*</span></label>
	                                <input id="GUVbcan" name="GUVbcan" style="width:100%">
                                </div><div class="mb-3 aTev IHoUa EaQvaRy nHvvc Fclm mImEmnw UHVGm lGpv RSvld IdSPym adEFxQ QnvnHn pRVwwUP Fdxv QEnSQ Tbmx GHmIb UdUV STIyUm eTSx EVSU wdRxSUy vvVQH oQQpe GcoGm SpxUax RVPlvpv TRcbdQ bdlyRpF dalpdI eSaEwSI TaPvxbm oFEPE SRVa IEpTo dVTETI mxPwTV nGFPew RvwPGxU moREabx yvlowVS mwTU boUQlPp Tvnn nQEceUU wwHemo IclaaHE bvEc yaVU wFlGpn nGoe RVeGbSy mt-6">
	                                <label class="form-label" for="Hylb">Visa Sub Type<span class="required">*</span></label>
	                                <input id="Hylb" name="Hylb" style="width:100%">
                                </div><div class="mb-3 nHvvc Tbmx QnvnHn Fclm RUpwIU SIlQbRo EaQvaRy moREabx dalpdI aTev UHVGm lGpv EVSU QEnSQ eSaEwSI SpxUax dVTETI RSvld IHoUa IEpTo mt-6">
	                                <label class="form-label" for="vvdT">Visa Sub Type<span class="required">*</span></label>
	                                <input id="vvdT" name="vvdT" style="width:100%">
                                </div><div class="mb-3 IclaaHE SpxUax IHoUa eSaEwSI QEnSQ boUQlPp aTev EaQvaRy dVTETI nQEceUU RSvld lGpv moREabx Tbmx EVSU dalpdI IEpTo SIlQbRo IoIneS QnvnHn nHvvc wdRxSUy Fclm UHVGm mt-6">
	                                <label class="form-label" for="UPvyH">Category<span class="required">*</span></label>
	                                <input id="UPvyH" name="UPvyH" style="width:100%">
                                </div><div class="mb-3 nGoe wwHemo nHvvc Tbmx bdlyRpF GHmIb bvEc mxPwTV mwTU RVeGbSy Fdxv SpxUax nGFPew RVPlvpv QnvnHn IEpTo dVTETI TaPvxbm wIcRx STIyUm QEnSQ nQEceUU eSaEwSI yvlowVS Fclm yaVU Tvnn EVSU GcoGm QUwbaFE EaQvaRy moREabx dalpdI RSvld oQQpe SRVa mImEmnw wFlGpn IHoUa eeVTo lGpv IclaaHE pRVwwUP aTev vvVQH TEaVl RvwPGxU boUQlPp oFEPE TRcbdQ adEFxQ xwnm UHVGm IdSPym wdRxSUy eTSx mt-6">
	                                <label class="form-label" for="Tamae">Category<span class="required">*</span></label>
	                                <input id="Tamae" name="Tamae" style="width:100%">
                                </div><div class="mb-3 yvlowVS dVTETI aTev QEnSQ Tbmx IdSPym mwTU nGFPew EaQvaRy QnvnHn IEpTo wFlGpn eTSx pRVwwUP Tvnn dalpdI nQEceUU dcVle IclaaHE vvVQH TaPvxbm oQQpe TRcbdQ lGpv UHVGm boUQlPp IHoUa RSvld eSaEwSI moREabx Fdxv EVSU SpxUax Fclm oFEPE nHvvc wdRxSUy mt-6">
	                                <label class="form-label" for="oPaPnpp">Category<span class="required">*</span></label>
	                                <input id="oPaPnpp" name="oPaPnpp" style="width:100%">
                                </div><div class="mb-3 QEnSQ IclaaHE EVSU QnvnHn dalpdI aTev bHcmVR lGpv dVTETI boUQlPp RSvld eTSx SpxUax moREabx UHVGm EaQvaRy nHvvc wFlGpn eSaEwSI IEpTo wdRxSUy nQEceUU Tbmx IHoUa Fclm mt-6">
	                                <label class="form-label" for="SpaUdG">Category<span class="required">*</span></label>
	                                <input id="SpaUdG" name="SpaUdG" style="width:100%">
                                </div><div class="mb-3 wwHemo bvEc IEpTo aTev nQEceUU QnvnHn pRVwwUP mwTU moREabx lGpv yvlowVS vvVQH Tbmx IHoUa Fclm EaQvaRy GcoGm oFEPE boUQlPp dVTETI wdRxSUy nHvvc mImEmnw lcRo Tvnn TaPvxbm eSaEwSI eTSx nGFPew dalpdI wFlGpn RSvld STIyUm Fdxv IclaaHE QEnSQ IdSPym oQQpe UHVGm SpxUax SIlQbRo TRcbdQ EVSU mt-6">
	                                <label class="form-label" for="EGHHIRo">Category<span class="required">*</span></label>
	                                <input id="EGHHIRo" name="EGHHIRo" style="width:100%">
                                </div><div class="mb-3 TRcbdQ dVTETI Fclm boUQlPp QnvnHn pRVwwUP TaPvxbm EaQvaRy SpxUax IclaaHE QEnSQ moREabx eSaEwSI EVSU aTev wFlGpn oQQpe oFEPE lGpv UHVGm yvlowVS RSvld IHoUa IEpTo Tbmx nQEceUU wdRxSUy vvVQH eTSx SIlQbRo nHvvc nGFPew dalpdI mt-3">
                            <label class="form-label">Appointment For<span class="text-danger">*</span></label>
                            <div class="d-flex gap-4">
                                <div class="form-check radio-bg-light">
                                    <input type="radio" name="afxGSGP" checked='checked'  class="form-check-input" value="Individual" id="selfxGSGP" checked onclick="OnAppointmentForChange(event,'xGSGP');" />
                                    <label class="form-check-label" for="selfxGSGP" style="margin-left: 22px;">
                                       Individual
                                    </label>
                                </div>
                               <div class="form-check radio-bg-light">
                                    <input type="radio"  name="afxGSGP"  class="form-check-input" value="Family" id="familyxGSGP" onclick="OnAppointmentForChange(event,'xGSGP');" />
                                    <label class="form-check-label" for="familyxGSGP" style="margin-left: 22px;">
                                        Family
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3 TRcbdQ dVTETI Fclm boUQlPp QnvnHn pRVwwUP TaPvxbm EaQvaRy SpxUax IclaaHE QEnSQ moREabx eSaEwSI EVSU aTev wFlGpn oQQpe oFEPE lGpv UHVGm yvlowVS RSvld IHoUa IEpTo Tbmx nQEceUU wdRxSUy vvVQH eTSx SIlQbRo nHvvc nGFPew dalpdI mt-3">
                            <div id="membersxGSGP" style='display:none;' >
	                            <label class="form-label" for="anxGSGP">Number Of Members<span class="required">*</span></label>
	                            <input id="anxGSGP" name="anxGSGP" style="width:100%">
                                <script>
                                        $(document).ready(function () {
                                            $("#anxGSGP").kendoDropDownList({
                                                optionLabel: "--Select--",
                                                dataTextField: "Name",
                                                dataValueField: "Value",
                                                filter: "contains",
                                                dataSource: applicantsNoData,
                                                open: onDrpOpen,
                                                close: onDrpClose,
                                                select: onDrpSelect
                                            });
                                        });
                                 </script>
                             </div>              
                        </div><div class="mb-3 Fclm UHVGm yvlowVS eTSx dalpdI SIlQbRo nQEceUU QnvnHn IHoUa SpxUax lGpv RSvld pRVwwUP boUQlPp moREabx aTev wdRxSUy EaQvaRy dVTETI Tbmx vvVQH nHvvc IclaaHE TRcbdQ QEnSQ eSaEwSI oFEPE EVSU nGFPew IEpTo oQQpe wFlGpn TaPvxbm mt-3">
                            <label class="form-label">Appointment For<span class="text-danger">*</span></label>
                            <div class="d-flex gap-4">
                                <div class="form-check radio-bg-light">
                                    <input type="radio" name="afmpvnx" checked='checked'  class="form-check-input" value="Individual" id="selfmpvnx" checked onclick="OnAppointmentForChange(event,'mpvnx');" />
                                    <label class="form-check-label" for="selfmpvnx" style="margin-left: 22px;">
                                       Individual
                                    </label>
                                </div>
                               <div class="form-check radio-bg-light">
                                    <input type="radio"  name="afmpvnx"  class="form-check-input" value="Family" id="familympvnx" onclick="OnAppointmentForChange(event,'mpvnx');" />
                                    <label class="form-check-label" for="familympvnx" style="margin-left: 22px;">
                                        Family
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3 Fclm UHVGm yvlowVS eTSx dalpdI SIlQbRo nQEceUU QnvnHn IHoUa SpxUax lGpv RSvld pRVwwUP boUQlPp moREabx aTev wdRxSUy EaQvaRy dVTETI Tbmx vvVQH nHvvc IclaaHE TRcbdQ QEnSQ eSaEwSI oFEPE EVSU nGFPew IEpTo oQQpe wFlGpn TaPvxbm mt-3">
                            <div id="membersmpvnx" style='display:none;' >
	                            <label class="form-label" for="anmpvnx">Number Of Members<span class="required">*</span></label>
	                            <input id="anmpvnx" name="anmpvnx" style="width:100%">
                                <script>
                                        $(document).ready(function () {
                                            $("#anmpvnx").kendoDropDownList({
                                                optionLabel: "--Select--",
                                                dataTextField: "Name",
                                                dataValueField: "Value",
                                                filter: "contains",
                                                dataSource: applicantsNoData,
                                                open: onDrpOpen,
                                                close: onDrpClose,
                                                select: onDrpSelect
                                            });
                                        });
                                 </script>
                             </div>              
                        </div><div class="mb-3 moREabx SpxUax SIlQbRo IEpTo UHVGm lGpv nHvvc RSvld dVTETI Fclm IHoUa aTev Tbmx mt-3">
                            <label class="form-label">Appointment For<span class="text-danger">*</span></label>
                            <div class="d-flex gap-4">
                                <div class="form-check radio-bg-light">
                                    <input type="radio" name="afmPIae" checked='checked'  class="form-check-input" value="Individual" id="selfmPIae" checked onclick="OnAppointmentForChange(event,'mPIae');" />
                                    <label class="form-check-label" for="selfmPIae" style="margin-left: 22px;">
                                       Individual
                                    </label>
                                </div>
                               <div class="form-check radio-bg-light">
                                    <input type="radio"  name="afmPIae"  class="form-check-input" value="Family" id="familymPIae" onclick="OnAppointmentForChange(event,'mPIae');" />
                                    <label class="form-check-label" for="familymPIae" style="margin-left: 22px;">
                                        Family
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3 moREabx SpxUax SIlQbRo IEpTo UHVGm lGpv nHvvc RSvld dVTETI Fclm IHoUa aTev Tbmx mt-3">
                            <div id="membersmPIae" style='display:none;' >
	                            <label class="form-label" for="anmPIae">Number Of Members<span class="required">*</span></label>
	                            <input id="anmPIae" name="anmPIae" style="width:100%">
                                <script>
                                        $(document).ready(function () {
                                            $("#anmPIae").kendoDropDownList({
                                                optionLabel: "--Select--",
                                                dataTextField: "Name",
                                                dataValueField: "Value",
                                                filter: "contains",
                                                dataSource: applicantsNoData,
                                                open: onDrpOpen,
                                                close: onDrpClose,
                                                select: onDrpSelect
                                            });
                                        });
                                 </script>
                             </div>              
                        </div><div class="mb-3 nQEceUU wdRxSUy SpxUax QnvnHn EaQvaRy wFlGpn eSaEwSI QEnSQ IHoUa eTSx UHVGm SIlQbRo IEpTo nHvvc dVTETI TaPvxbm Fclm lGpv IclaaHE EVSU nGFPew aTev boUQlPp Tbmx moREabx RSvld dalpdI mt-3">
                            <label class="form-label">Appointment For<span class="text-danger">*</span></label>
                            <div class="d-flex gap-4">
                                <div class="form-check radio-bg-light">
                                    <input type="radio" name="afwHnoFn" checked='checked'  class="form-check-input" value="Individual" id="selfwHnoFn" checked onclick="OnAppointmentForChange(event,'wHnoFn');" />
                                    <label class="form-check-label" for="selfwHnoFn" style="margin-left: 22px;">
                                       Individual
                                    </label>
                                </div>
                               <div class="form-check radio-bg-light">
                                    <input type="radio"  name="afwHnoFn"  class="form-check-input" value="Family" id="familywHnoFn" onclick="OnAppointmentForChange(event,'wHnoFn');" />
                                    <label class="form-check-label" for="familywHnoFn" style="margin-left: 22px;">
                                        Family
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3 nQEceUU wdRxSUy SpxUax QnvnHn EaQvaRy wFlGpn eSaEwSI QEnSQ IHoUa eTSx UHVGm SIlQbRo IEpTo nHvvc dVTETI TaPvxbm Fclm lGpv IclaaHE EVSU nGFPew aTev boUQlPp Tbmx moREabx RSvld dalpdI mt-3">
                            <div id="memberswHnoFn" style='display:none;' >
	                            <label class="form-label" for="anwHnoFn">Number Of Members<span class="required">*</span></label>
	                            <input id="anwHnoFn" name="anwHnoFn" style="width:100%">
                                <script>
                                        $(document).ready(function () {
                                            $("#anwHnoFn").kendoDropDownList({
                                                optionLabel: "--Select--",
                                                dataTextField: "Name",
                                                dataValueField: "Value",
                                                filter: "contains",
                                                dataSource: applicantsNoData,
                                                open: onDrpOpen,
                                                close: onDrpClose,
                                                select: onDrpSelect
                                            });
                                        });
                                 </script>
                             </div>              
                        </div><div class="mb-3 dVTETI moREabx Fclm IHoUa UHVGm RSvld Tbmx oQcmdly nHvvc QnvnHn aTev SpxUax lGpv IEpTo mt-3">
                            <label class="form-label">Appointment For<span class="text-danger">*</span></label>
                            <div class="d-flex gap-4">
                                <div class="form-check radio-bg-light">
                                    <input type="radio" name="afFbpTQcI" checked='checked'  class="form-check-input" value="Individual" id="selfFbpTQcI" checked onclick="OnAppointmentForChange(event,'FbpTQcI');" />
                                    <label class="form-check-label" for="selfFbpTQcI" style="margin-left: 22px;">
                                       Individual
                                    </label>
                                </div>
                               <div class="form-check radio-bg-light">
                                    <input type="radio"  name="afFbpTQcI"  class="form-check-input" value="Family" id="familyFbpTQcI" onclick="OnAppointmentForChange(event,'FbpTQcI');" />
                                    <label class="form-check-label" for="familyFbpTQcI" style="margin-left: 22px;">
                                        Family
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3 dVTETI moREabx Fclm IHoUa UHVGm RSvld Tbmx oQcmdly nHvvc QnvnHn aTev SpxUax lGpv IEpTo mt-3">
                            <div id="membersFbpTQcI" style='display:none;' >
	                            <label class="form-label" for="anFbpTQcI">Number Of Members<span class="required">*</span></label>
	                            <input id="anFbpTQcI" name="anFbpTQcI" style="width:100%">
                                <script>
                                        $(document).ready(function () {
                                            $("#anFbpTQcI").kendoDropDownList({
                                                optionLabel: "--Select--",
                                                dataTextField: "Name",
                                                dataValueField: "Value",
                                                filter: "contains",
                                                dataSource: applicantsNoData,
                                                open: onDrpOpen,
                                                close: onDrpClose,
                                                select: onDrpSelect
                                            });
                                        });
                                 </script>
                             </div>              
                        </div>
<script>$(document).ready(function () {
                                        
$("#VImxxn").kendoDropDownList({
                                                    optionLabel: "--Select--",
                                                    dataTextField: "Name",
                                                    dataValueField: "Id",
                                                    filter: "contains",
                                                    dataSource: lPHvnm,
                                                    change:VImxxn_change,
                                                    open: onDrpOpen,
                                                    autoBind:false,
                                                    close: onDrpClose,
                                                    select: onDrpSelect
                                               });
$("#GGxlx").kendoDropDownList({
                                                    optionLabel: "--Select--",
                                                    dataTextField: "Name",
                                                    dataValueField: "Id",
                                                    filter: "contains",
                                                    dataSource: HydFdoF,
                                                    change:GGxlx_change,
                                                    open: onDrpOpen,
                                                    autoBind:false,
                                                    close: onDrpClose,
                                                    select: onDrpSelect
                                               });
$("#IyeRyI").kendoDropDownList({
                                                    optionLabel: "--Select--",
                                                    dataTextField: "Name",
                                                    dataValueField: "Id",
                                                    filter: "contains",
                                                    dataSource: lPHvnm,
                                                    change:IyeRyI_change,
                                                    open: onDrpOpen,
                                                    autoBind:false,
                                                    close: onDrpClose,
                                                    select: onDrpSelect
                                               });
$("#UbPR").kendoDropDownList({
                                                    optionLabel: "--Select--",
                                                    dataTextField: "Name",
                                                    dataValueField: "Id",
                                                    filter: "contains",
                                                    dataSource: lPHvnm,
                                                    change:UbPR_change,
                                                    open: onDrpOpen,
                                                    autoBind:false,
                                                    close: onDrpClose,
                                                    select: onDrpSelect
                                               });
$("#mSGExU").kendoDropDownList({
                                                    optionLabel: "--Select--",
                                                    dataTextField: "Name",
                                                    dataValueField: "Id",
                                                    filter: "contains",
                                                    dataSource: lPHvnm,
                                                    change:mSGExU_change,
                                                    open: onDrpOpen,
                                                    autoBind:false,
                                                    close: onDrpClose,
                                                    select: onDrpSelect
                                               });
$("#UanTRc").kendoDropDownList({
                                                    optionLabel: "--Select--",
                                                    dataTextField: "Name",
                                                    dataValueField: "Id",
                                                    filter: "contains",
                                                    dataSource: visaTypeFilterData,
                                                    change:UanTRc_change,
                                                    open: onDrpOpen,
                                                    autoBind:false,
                                                    close: onDrpClose,
                                                    select: onDrpSelect

                                               });
$("#ddTe").kendoDropDownList({
                                                    optionLabel: "--Select--",
                                                    dataTextField: "Name",
                                                    dataValueField: "Id",
                                                    filter: "contains",
                                                    dataSource: visaTypeFilterData,
                                                    change:ddTe_change,
                                                    open: onDrpOpen,
                                                    autoBind:false,
                                                    close: onDrpClose,
                                                    select: onDrpSelect

                                               });
$("#cPEPnUS").kendoDropDownList({
                                                    optionLabel: "--Select--",
                                                    dataTextField: "Name",
                                                    dataValueField: "Id",
                                                    filter: "contains",
                                                    dataSource: visaTypeFilterData,
                                                    change:cPEPnUS_change,
                                                    open: onDrpOpen,
                                                    autoBind:false,
                                                    close: onDrpClose,
                                                    select: onDrpSelect

                                               });
$("#eQwIvv").kendoDropDownList({
                                                    optionLabel: "--Select--",
                                                    dataTextField: "Name",
                                                    dataValueField: "Id",
                                                    filter: "contains",
                                                    dataSource: visaTypeFilterData,
                                                    change:eQwIvv_change,
                                                    open: onDrpOpen,
                                                    autoBind:false,
                                                    close: onDrpClose,
                                                    select: onDrpSelect

                                               });
$("#vlnRQQo").kendoDropDownList({
                                                    optionLabel: "--Select--",
                                                    dataTextField: "Name",
                                                    dataValueField: "Id",
                                                    filter: "contains",
                                                    dataSource: visaTypeFilterData,
                                                    change:vlnRQQo_change,
                                                    open: onDrpOpen,
                                                    autoBind:false,
                                                    close: onDrpClose,
                                                    select: onDrpSelect

                                               });
$("#TEURn").kendoDropDownList({
                                                    optionLabel: "--Select--",
                                                    dataTextField: "Name",
                                                    dataValueField: "Id",
                                                    filter: "contains",
                                                    dataSource: visasubIdFilterData,
                                                    open: onDrpOpen,
                                                    change:TEURn_change,
                                                    autoBind:false,
                                                    close: onDrpClose,
                                                    select: onDrpSelect
                                               });
$("#pcRFne").kendoDropDownList({
                                                    optionLabel: "--Select--",
                                                    dataTextField: "Name",
                                                    dataValueField: "Id",
                                                    filter: "contains",
                                                    dataSource: visasubIdFilterData,
                                                    open: onDrpOpen,
                                                    change:pcRFne_change,
                                                    autoBind:false,
                                                    close: onDrpClose,
                                                    select: onDrpSelect
                                               });
$("#GUVbcan").kendoDropDownList({
                                                    optionLabel: "--Select--",
                                                    dataTextField: "Name",
                                                    dataValueField: "Id",
                                                    filter: "contains",
                                                    dataSource: visasubIdFilterData,
                                                    open: onDrpOpen,
                                                    change:GUVbcan_change,
                                                    autoBind:false,
                                                    close: onDrpClose,
                                                    select: onDrpSelect
                                               });
$("#Hylb").kendoDropDownList({
                                                    optionLabel: "--Select--",
                                                    dataTextField: "Name",
                                                    dataValueField: "Id",
                                                    filter: "contains",
                                                    dataSource: visasubIdFilterData,
                                                    open: onDrpOpen,
                                                    change:Hylb_change,
                                                    autoBind:false,
                                                    close: onDrpClose,
                                                    select: onDrpSelect
                                               });
$("#vvdT").kendoDropDownList({
                                                    optionLabel: "--Select--",
                                                    dataTextField: "Name",
                                                    dataValueField: "Id",
                                                    filter: "contains",
                                                    dataSource: visasubIdFilterData,
                                                    open: onDrpOpen,
                                                    change:vvdT_change,
                                                    autoBind:false,
                                                    close: onDrpClose,
                                                    select: onDrpSelect
                                               });
$("#xGSGP").kendoDropDownList({
                                                    optionLabel: "--Select--",
                                                    dataTextField: "Name",
                                                    dataValueField: "Id",
                                                    filter: "contains",
                                                    dataSource: visaTypeFilterData,
                                                    change:xGSGP_change,
                                                    open: onDrpOpen,
                                                    autoBind:false,
                                                    close: onDrpClose,
                                                    select: onDrpSelect

                                               });
$("#mpvnx").kendoDropDownList({
                                                    optionLabel: "--Select--",
                                                    dataTextField: "Name",
                                                    dataValueField: "Id",
                                                    filter: "contains",
                                                    dataSource: visaTypeFilterData,
                                                    change:mpvnx_change,
                                                    open: onDrpOpen,
                                                    autoBind:false,
                                                    close: onDrpClose,
                                                    select: onDrpSelect

                                               });
$("#mPIae").kendoDropDownList({
                                                    optionLabel: "--Select--",
                                                    dataTextField: "Name",
                                                    dataValueField: "Id",
                                                    filter: "contains",
                                                    dataSource: visaTypeFilterData,
                                                    change:mPIae_change,
                                                    open: onDrpOpen,
                                                    autoBind:false,
                                                    close: onDrpClose,
                                                    select: onDrpSelect

                                               });
$("#wHnoFn").kendoDropDownList({
                                                    optionLabel: "--Select--",
                                                    dataTextField: "Name",
                                                    dataValueField: "Id",
                                                    filter: "contains",
                                                    dataSource: visaTypeFilterData,
                                                    change:wHnoFn_change,
                                                    open: onDrpOpen,
                                                    autoBind:false,
                                                    close: onDrpClose,
                                                    select: onDrpSelect

                                               });
$("#FbpTQcI").kendoDropDownList({
                                                    optionLabel: "--Select--",
                                                    dataTextField: "Name",
                                                    dataValueField: "Id",
                                                    filter: "contains",
                                                    dataSource: visaTypeFilterData,
                                                    change:FbpTQcI_change,
                                                    open: onDrpOpen,
                                                    autoBind:false,
                                                    close: onDrpClose,
                                                    select: onDrpSelect

                                               });
$("#UPvyH").kendoDropDownList({
                                                    optionLabel: "--Select--",
                                                    dataTextField: "Name",
                                                    dataValueField: "Id",
                                                    filter: "contains",
                                                    dataSource: categoryData,
                                                    open: onDrpOpen,
                                                    change:UPvyH_change,
                                                    autoBind:false,
                                                    close: onDrpClose,
                                                    select: onDrpSelect
                                               });
$("#Tamae").kendoDropDownList({
                                                    optionLabel: "--Select--",
                                                    dataTextField: "Name",
                                                    dataValueField: "Id",
                                                    filter: "contains",
                                                    dataSource: categoryData,
                                                    open: onDrpOpen,
                                                    change:Tamae_change,
                                                    autoBind:false,
                                                    close: onDrpClose,
                                                    select: onDrpSelect
                                               });
$("#oPaPnpp").kendoDropDownList({
                                                    optionLabel: "--Select--",
                                                    dataTextField: "Name",
                                                    dataValueField: "Id",
                                                    filter: "contains",
                                                    dataSource: categoryData,
                                                    open: onDrpOpen,
                                                    change:oPaPnpp_change,
                                                    autoBind:false,
                                                    close: onDrpClose,
                                                    select: onDrpSelect
                                               });
$("#SpaUdG").kendoDropDownList({
                                                    optionLabel: "--Select--",
                                                    dataTextField: "Name",
                                                    dataValueField: "Id",
                                                    filter: "contains",
                                                    dataSource: categoryData,
                                                    open: onDrpOpen,
                                                    change:SpaUdG_change,
                                                    autoBind:false,
                                                    close: onDrpClose,
                                                    select: onDrpSelect
                                               });
$("#EGHHIRo").kendoDropDownList({
                                                    optionLabel: "--Select--",
                                                    dataTextField: "Name",
                                                    dataValueField: "Id",
                                                    filter: "contains",
                                                    dataSource: categoryData,
                                                    open: onDrpOpen,
                                                    change:EGHHIRo_change,
                                                    autoBind:false,
                                                    close: onDrpClose,
                                                    select: onDrpSelect
                                               });
                                         
                                         
                                         
                                         
                                         
                                        fMOZK($('.myTTSS'));sOJuf($('.myTTSS'));fMOZK($('.myTTSS'));gXhtZj($('.myTTSS'));gXhtZj($('.myTTSS'));CDJurfz($('.myTTSS'));CgqOz($('.myTTSS'));gMusYCM($('.myTTSS'));CgqOz($('.myTTSS'));CDJurfz($('.myTTSS'));AKtk($('.myTTSS'));ZAjrjYh($('.myTTSS'));zfighu($('.myTTSS'));COqzW($('.yRaw'));sqhh($('.yRaw'));sOJuf($('.yRaw'));COqzW($('.GdQbG'));sqhh($('.GdQbG'));ZAjrjYh($('.lFwwm'));ZAjrjYh($('.lFwwm'));zfighu($('.lFwwm'));CDJurfz($('.lFwwm'));guuKuu($('.lFwwm'));CgqOz($('.lFwwm'));gMusYCM($('.lFwwm'));fMOZK($('.lFwwm'));gXhtZj($('.lFwwm'));sOJuf($('.lFwwm'));sOJuf($('.lFwwm'));gXhtZj($('.lFwwm'));qBfrNC($('.clFyHHp'));AKtk($('.clFyHHp'));zfighu($('.clFyHHp'));AtDO($('.clFyHHp'));gMusYCM($('.clFyHHp'));COqzW($('.clFyHHp'));sOJuf($('.clFyHHp'));sqhh($('.clFyHHp'));sOJuf($('.clFyHHp'));sqhh($('.clFyHHp'));zfighu($('.ynPRwl'));zfighu($('.ynPRwl'));AKtk($('.ynPRwl'));gMusYCM($('.ynPRwl'));CgqOz($('.ynPRwl'));guuKuu($('.ynPRwl'));gMusYCM($('.ynPRwl'));fMOZK($('.ynPRwl'));gXhtZj($('.ynPRwl'));COqzW($('.ynPRwl'));sqhh($('.ynPRwl'));sOJuf($('.vFFby'));fMOZK($('.vFFby'));sOJuf($('.vFFby'));sOJuf($('.vFFby'));sqhh($('.vFFby'));guuKuu($('.vFFby'));guuKuu($('.vFFby'));CgqOz($('.vFFby'));AtDO($('.vFFby'));zfighu($('.vFFby'));jONMC($('.vFFby'));ZAjrjYh($('.vFFby'));AKtk($('.vFFby'));zfighu($('.vFFby'));jONMC($('.Ubmc'));zfighu($('.Ubmc'));qBfrNC($('.Ubmc'));ZAjrjYh($('.Ubmc'));ZAjrjYh($('.Ubmc'));CgqOz($('.Ubmc'));gMusYCM($('.Ubmc'));CgqOz($('.Ubmc'));sOJuf($('.Ubmc'));sqhh($('.Ubmc'));fMOZK($('.Ubmc'));qBfrNC($('.FedFbG'));qBfrNC($('.FedFbG'));zfighu($('.FedFbG'));guuKuu($('.FedFbG'));CDJurfz($('.FedFbG'));CgqOz($('.FedFbG'));guuKuu($('.FedFbG'));sqhh($('.FedFbG'));sOJuf($('.FedFbG'));sOJuf($('.FedFbG'));COqzW($('.dPwQyd'));sqhh($('.dPwQyd'));COqzW($('.dPwQyd'));sqhh($('.dPwQyd'));sOJuf($('.damd'));fMOZK($('.damd'));COqzW($('.damd'));zfighu($('.IdaFwym'));ZAjrjYh($('.IdaFwym'));jONMC($('.IdaFwym'));CgqOz($('.IdaFwym'));guuKuu($('.IdaFwym'));AtDO($('.IdaFwym'));sOJuf($('.IdaFwym'));sqhh($('.IdaFwym'));fMOZK($('.IdaFwym'));gXhtZj($('.IdaFwym'));qBfrNC($('.nGbp'));qBfrNC($('.nGbp'));ZAjrjYh($('.nGbp'));jONMC($('.nGbp'));CgqOz($('.nGbp'));CgqOz($('.nGbp'));CDJurfz($('.nGbp'));guuKuu($('.nGbp'));CDJurfz($('.nGbp'));sqhh($('.nGbp'));gXhtZj($('.nGbp'));gXhtZj($('.nGbp'));fMOZK($('.UdUV'));fMOZK($('.UdUV'));COqzW($('.UdUV'));gXhtZj($('.UdUV'));gMusYCM($('.UdUV'));CgqOz($('.UdUV'));AKtk($('.UdUV'));AKtk($('.UdUV'));qBfrNC($('.UdUV'));qBfrNC($('.UdUV'));qBfrNC($('.UdUV'));fMOZK($('.RUpwIU'));sOJuf($('.RUpwIU'));sOJuf($('.IoIneS'));COqzW($('.IoIneS'));gXhtZj($('.eeVTo'));sqhh($('.eeVTo'));COqzW($('.eeVTo'));gMusYCM($('.eeVTo'));gMusYCM($('.eeVTo'));gMusYCM($('.eeVTo'));guuKuu($('.eeVTo'));AtDO($('.eeVTo'));zfighu($('.eeVTo'));AKtk($('.eeVTo'));jONMC($('.eeVTo'));ZAjrjYh($('.dcVle'));zfighu($('.dcVle'));AKtk($('.dcVle'));CgqOz($('.dcVle'));CgqOz($('.dcVle'));CgqOz($('.dcVle'));COqzW($('.dcVle'));sOJuf($('.dcVle'));sOJuf($('.dcVle'));gXhtZj($('.dcVle'));sqhh($('.dcVle'));AKtk($('.bHcmVR'));jONMC($('.bHcmVR'));AKtk($('.bHcmVR'));jONMC($('.bHcmVR'));CDJurfz($('.bHcmVR'));CDJurfz($('.bHcmVR'));AtDO($('.bHcmVR'));COqzW($('.bHcmVR'));fMOZK($('.bHcmVR'));COqzW($('.bHcmVR'));COqzW($('.lcRo'));fMOZK($('.lcRo'));
                                    });
                                    
function VImxxn_change(e) {
                                                
                                            locationDataItem = e.sender.dataItem();
                                            if(locationDataItem.Id!=null){
                                                visasubIdFilterData = visasubIdData.filter(v => v.Id==='0');
                                                $("#TEURn").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#pcRFne").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#GUVbcan").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#Hylb").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#vvdT").data("kendoDropDownList").setDataSource(visasubIdFilterData);

                                                visaTypeFilterData = visaIdData.filter(v => locationDataItem.VisaTypeIds.includes(v.Id));
                                                $("#UanTRc").data("kendoDropDownList").setDataSource(visaTypeFilterData);
$("#ddTe").data("kendoDropDownList").setDataSource(visaTypeFilterData);
$("#cPEPnUS").data("kendoDropDownList").setDataSource(visaTypeFilterData);
$("#eQwIvv").data("kendoDropDownList").setDataSource(visaTypeFilterData);
$("#vlnRQQo").data("kendoDropDownList").setDataSource(visaTypeFilterData);

                                            }
                                        }
function GGxlx_change(e) {
                                                
                                            locationDataItem = e.sender.dataItem();
                                            if(locationDataItem.Id!=null){
                                                visasubIdFilterData = visasubIdData.filter(v => v.Id==='0');
                                                $("#TEURn").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#pcRFne").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#GUVbcan").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#Hylb").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#vvdT").data("kendoDropDownList").setDataSource(visasubIdFilterData);

                                                visaTypeFilterData = visaIdData.filter(v => locationDataItem.VisaTypeIds.includes(v.Id));
                                                $("#UanTRc").data("kendoDropDownList").setDataSource(visaTypeFilterData);
$("#ddTe").data("kendoDropDownList").setDataSource(visaTypeFilterData);
$("#cPEPnUS").data("kendoDropDownList").setDataSource(visaTypeFilterData);
$("#eQwIvv").data("kendoDropDownList").setDataSource(visaTypeFilterData);
$("#vlnRQQo").data("kendoDropDownList").setDataSource(visaTypeFilterData);

                                            }
                                        }
function IyeRyI_change(e) {
                                                
                                            locationDataItem = e.sender.dataItem();
                                            if(locationDataItem.Id!=null){
                                                visasubIdFilterData = visasubIdData.filter(v => v.Id==='0');
                                                $("#TEURn").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#pcRFne").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#GUVbcan").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#Hylb").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#vvdT").data("kendoDropDownList").setDataSource(visasubIdFilterData);

                                                visaTypeFilterData = visaIdData.filter(v => locationDataItem.VisaTypeIds.includes(v.Id));
                                                $("#UanTRc").data("kendoDropDownList").setDataSource(visaTypeFilterData);
$("#ddTe").data("kendoDropDownList").setDataSource(visaTypeFilterData);
$("#cPEPnUS").data("kendoDropDownList").setDataSource(visaTypeFilterData);
$("#eQwIvv").data("kendoDropDownList").setDataSource(visaTypeFilterData);
$("#vlnRQQo").data("kendoDropDownList").setDataSource(visaTypeFilterData);

                                            }
                                        }
function UbPR_change(e) {
                                                
                                            locationDataItem = e.sender.dataItem();
                                            if(locationDataItem.Id!=null){
                                                visasubIdFilterData = visasubIdData.filter(v => v.Id==='0');
                                                $("#TEURn").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#pcRFne").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#GUVbcan").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#Hylb").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#vvdT").data("kendoDropDownList").setDataSource(visasubIdFilterData);

                                                visaTypeFilterData = visaIdData.filter(v => locationDataItem.VisaTypeIds.includes(v.Id));
                                                $("#UanTRc").data("kendoDropDownList").setDataSource(visaTypeFilterData);
$("#ddTe").data("kendoDropDownList").setDataSource(visaTypeFilterData);
$("#cPEPnUS").data("kendoDropDownList").setDataSource(visaTypeFilterData);
$("#eQwIvv").data("kendoDropDownList").setDataSource(visaTypeFilterData);
$("#vlnRQQo").data("kendoDropDownList").setDataSource(visaTypeFilterData);

                                            }
                                        }
function mSGExU_change(e) {
                                                
                                            locationDataItem = e.sender.dataItem();
                                            if(locationDataItem.Id!=null){
                                                visasubIdFilterData = visasubIdData.filter(v => v.Id==='0');
                                                $("#TEURn").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#pcRFne").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#GUVbcan").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#Hylb").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#vvdT").data("kendoDropDownList").setDataSource(visasubIdFilterData);

                                                visaTypeFilterData = visaIdData.filter(v => locationDataItem.VisaTypeIds.includes(v.Id));
                                                $("#UanTRc").data("kendoDropDownList").setDataSource(visaTypeFilterData);
$("#ddTe").data("kendoDropDownList").setDataSource(visaTypeFilterData);
$("#cPEPnUS").data("kendoDropDownList").setDataSource(visaTypeFilterData);
$("#eQwIvv").data("kendoDropDownList").setDataSource(visaTypeFilterData);
$("#vlnRQQo").data("kendoDropDownList").setDataSource(visaTypeFilterData);

                                            }
                                        }
 function UanTRc_change(e) {
                                            var dataItem = e.sender.dataItem();
                                            if(dataItem.Id!=null){
                                                visasubIdFilterData = visasubIdData.filter(v => v.Value === dataItem.Id);
                                                if(locationDataItem!=null && locationDataItem.VisaSubTypeIds!=null){
                                                    visasubIdFilterData = visasubIdFilterData.filter(v => locationDataItem.VisaSubTypeIds.includes(v.Id));
                                                }
                                                $("#TEURn").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#pcRFne").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#GUVbcan").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#Hylb").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#vvdT").data("kendoDropDownList").setDataSource(visasubIdFilterData);

                                            } 
                                            if (dataItem.VisaTypeCode != null)
                                            {
                                               $('#VisaTypeModel').modal('show');
                                            }                                            
                                        }
                                        function OnVisaReject() {
                                                $('#VisaTypeModel').modal('hide');
                                                window.location.href = '/DZA/appointmentdata/myappointments';
                                        }
 function ddTe_change(e) {
                                            var dataItem = e.sender.dataItem();
                                            if(dataItem.Id!=null){
                                                visasubIdFilterData = visasubIdData.filter(v => v.Value === dataItem.Id);
                                                if(locationDataItem!=null && locationDataItem.VisaSubTypeIds!=null){
                                                    visasubIdFilterData = visasubIdFilterData.filter(v => locationDataItem.VisaSubTypeIds.includes(v.Id));
                                                }
                                                $("#TEURn").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#pcRFne").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#GUVbcan").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#Hylb").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#vvdT").data("kendoDropDownList").setDataSource(visasubIdFilterData);

                                            } 
                                            if (dataItem.VisaTypeCode != null)
                                            {
                                               $('#VisaTypeModel').modal('show');
                                            }                                            
                                        }
                                        function OnVisaReject() {
                                                $('#VisaTypeModel').modal('hide');
                                                window.location.href = '/DZA/appointmentdata/myappointments';
                                        }
 function cPEPnUS_change(e) {
                                            var dataItem = e.sender.dataItem();
                                            if(dataItem.Id!=null){
                                                visasubIdFilterData = visasubIdData.filter(v => v.Value === dataItem.Id);
                                                if(locationDataItem!=null && locationDataItem.VisaSubTypeIds!=null){
                                                    visasubIdFilterData = visasubIdFilterData.filter(v => locationDataItem.VisaSubTypeIds.includes(v.Id));
                                                }
                                                $("#TEURn").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#pcRFne").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#GUVbcan").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#Hylb").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#vvdT").data("kendoDropDownList").setDataSource(visasubIdFilterData);

                                            } 
                                            if (dataItem.VisaTypeCode != null)
                                            {
                                               $('#VisaTypeModel').modal('show');
                                            }                                            
                                        }
                                        function OnVisaReject() {
                                                $('#VisaTypeModel').modal('hide');
                                                window.location.href = '/DZA/appointmentdata/myappointments';
                                        }
 function eQwIvv_change(e) {
                                            var dataItem = e.sender.dataItem();
                                            if(dataItem.Id!=null){
                                                visasubIdFilterData = visasubIdData.filter(v => v.Value === dataItem.Id);
                                                if(locationDataItem!=null && locationDataItem.VisaSubTypeIds!=null){
                                                    visasubIdFilterData = visasubIdFilterData.filter(v => locationDataItem.VisaSubTypeIds.includes(v.Id));
                                                }
                                                $("#TEURn").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#pcRFne").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#GUVbcan").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#Hylb").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#vvdT").data("kendoDropDownList").setDataSource(visasubIdFilterData);

                                            } 
                                            if (dataItem.VisaTypeCode != null)
                                            {
                                               $('#VisaTypeModel').modal('show');
                                            }                                            
                                        }
                                        function OnVisaReject() {
                                                $('#VisaTypeModel').modal('hide');
                                                window.location.href = '/DZA/appointmentdata/myappointments';
                                        }
 function vlnRQQo_change(e) {
                                            var dataItem = e.sender.dataItem();
                                            if(dataItem.Id!=null){
                                                visasubIdFilterData = visasubIdData.filter(v => v.Value === dataItem.Id);
                                                if(locationDataItem!=null && locationDataItem.VisaSubTypeIds!=null){
                                                    visasubIdFilterData = visasubIdFilterData.filter(v => locationDataItem.VisaSubTypeIds.includes(v.Id));
                                                }
                                                $("#TEURn").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#pcRFne").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#GUVbcan").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#Hylb").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#vvdT").data("kendoDropDownList").setDataSource(visasubIdFilterData);

                                            } 
                                            if (dataItem.VisaTypeCode != null)
                                            {
                                               $('#VisaTypeModel').modal('show');
                                            }                                            
                                        }
                                        function OnVisaReject() {
                                                $('#VisaTypeModel').modal('hide');
                                                window.location.href = '/DZA/appointmentdata/myappointments';
                                        }
 function TEURn_change(e) {

                                            var dataItem = e.sender.dataItem();                          
                                            $("#DataSource").val(dataItem.Code);
                                                if (dataItem.VisaSubTypeCode === 'SCHENGEN_VISA_ORAN_ONE')
                                                {
                                                    $('#Oran1Visatype').modal('show');
                                                }
                                                else if (dataItem.VisaSubTypeCode === 'SCHENGEN_VISA_ORAN_TWO')
                                                {
                                                    $('#Oran2Visatype').modal('show');
                                                }
                                                else if (dataItem.VisaSubTypeCode === 'SCHENGEN_VISA_ORAN_THREE')
                                                {
                                                    $('#Oran3Visatype').modal('show');
                                                }
                                                else if (dataItem.VisaSubTypeCode === 'SCHENGEN_VISA_ORAN_FOUR')
                                                {
                                                    $('#Oran4Visatype').modal('show');
                                                }
                                                else if (dataItem.VisaSubTypeCode === 'SCHENGEN_VISA_ALG_ONE')
                                                {
                                                    $('#Alg1Visatype').modal('show');
                                                }
                                                else if (dataItem.VisaSubTypeCode === 'SCHENGEN_VISA_ALG_TWO')
                                                {
                                                    $('#Alg2Visatype').modal('show');
                                                }
                                                else if (dataItem.VisaSubTypeCode === 'SCHENGEN_VISA_ALG_THREE')
                                                {
                                                    $('#Alg3Visatype').modal('show');
                                                }
                                                else if (dataItem.VisaSubTypeCode === 'SCHENGEN_VISA_ALG_FOUR')
                                                {
                                                    $('#Alg4Visatype').modal('show');
                                                }
                                                else if (dataItem.VisaSubTypeCode === 'FAMILY_GROUP')
                                                {
                                                    $('#FamilyVisaSubtype').modal('show');
                                                }
                                        }
 function pcRFne_change(e) {

                                            var dataItem = e.sender.dataItem();                          
                                            $("#DataSource").val(dataItem.Code);
                                                if (dataItem.VisaSubTypeCode === 'SCHENGEN_VISA_ORAN_ONE')
                                                {
                                                    $('#Oran1Visatype').modal('show');
                                                }
                                                else if (dataItem.VisaSubTypeCode === 'SCHENGEN_VISA_ORAN_TWO')
                                                {
                                                    $('#Oran2Visatype').modal('show');
                                                }
                                                else if (dataItem.VisaSubTypeCode === 'SCHENGEN_VISA_ORAN_THREE')
                                                {
                                                    $('#Oran3Visatype').modal('show');
                                                }
                                                else if (dataItem.VisaSubTypeCode === 'SCHENGEN_VISA_ORAN_FOUR')
                                                {
                                                    $('#Oran4Visatype').modal('show');
                                                }
                                                else if (dataItem.VisaSubTypeCode === 'SCHENGEN_VISA_ALG_ONE')
                                                {
                                                    $('#Alg1Visatype').modal('show');
                                                }
                                                else if (dataItem.VisaSubTypeCode === 'SCHENGEN_VISA_ALG_TWO')
                                                {
                                                    $('#Alg2Visatype').modal('show');
                                                }
                                                else if (dataItem.VisaSubTypeCode === 'SCHENGEN_VISA_ALG_THREE')
                                                {
                                                    $('#Alg3Visatype').modal('show');
                                                }
                                                else if (dataItem.VisaSubTypeCode === 'SCHENGEN_VISA_ALG_FOUR')
                                                {
                                                    $('#Alg4Visatype').modal('show');
                                                }
                                                else if (dataItem.VisaSubTypeCode === 'FAMILY_GROUP')
                                                {
                                                    $('#FamilyVisaSubtype').modal('show');
                                                }
                                        }
 function GUVbcan_change(e) {

                                            var dataItem = e.sender.dataItem();                          
                                            $("#DataSource").val(dataItem.Code);
                                                if (dataItem.VisaSubTypeCode === 'SCHENGEN_VISA_ORAN_ONE')
                                                {
                                                    $('#Oran1Visatype').modal('show');
                                                }
                                                else if (dataItem.VisaSubTypeCode === 'SCHENGEN_VISA_ORAN_TWO')
                                                {
                                                    $('#Oran2Visatype').modal('show');
                                                }
                                                else if (dataItem.VisaSubTypeCode === 'SCHENGEN_VISA_ORAN_THREE')
                                                {
                                                    $('#Oran3Visatype').modal('show');
                                                }
                                                else if (dataItem.VisaSubTypeCode === 'SCHENGEN_VISA_ORAN_FOUR')
                                                {
                                                    $('#Oran4Visatype').modal('show');
                                                }
                                                else if (dataItem.VisaSubTypeCode === 'SCHENGEN_VISA_ALG_ONE')
                                                {
                                                    $('#Alg1Visatype').modal('show');
                                                }
                                                else if (dataItem.VisaSubTypeCode === 'SCHENGEN_VISA_ALG_TWO')
                                                {
                                                    $('#Alg2Visatype').modal('show');
                                                }
                                                else if (dataItem.VisaSubTypeCode === 'SCHENGEN_VISA_ALG_THREE')
                                                {
                                                    $('#Alg3Visatype').modal('show');
                                                }
                                                else if (dataItem.VisaSubTypeCode === 'SCHENGEN_VISA_ALG_FOUR')
                                                {
                                                    $('#Alg4Visatype').modal('show');
                                                }
                                                else if (dataItem.VisaSubTypeCode === 'FAMILY_GROUP')
                                                {
                                                    $('#FamilyVisaSubtype').modal('show');
                                                }
                                        }
 function Hylb_change(e) {

                                            var dataItem = e.sender.dataItem();                          
                                            $("#DataSource").val(dataItem.Code);
                                                if (dataItem.VisaSubTypeCode === 'SCHENGEN_VISA_ORAN_ONE')
                                                {
                                                    $('#Oran1Visatype').modal('show');
                                                }
                                                else if (dataItem.VisaSubTypeCode === 'SCHENGEN_VISA_ORAN_TWO')
                                                {
                                                    $('#Oran2Visatype').modal('show');
                                                }
                                                else if (dataItem.VisaSubTypeCode === 'SCHENGEN_VISA_ORAN_THREE')
                                                {
                                                    $('#Oran3Visatype').modal('show');
                                                }
                                                else if (dataItem.VisaSubTypeCode === 'SCHENGEN_VISA_ORAN_FOUR')
                                                {
                                                    $('#Oran4Visatype').modal('show');
                                                }
                                                else if (dataItem.VisaSubTypeCode === 'SCHENGEN_VISA_ALG_ONE')
                                                {
                                                    $('#Alg1Visatype').modal('show');
                                                }
                                                else if (dataItem.VisaSubTypeCode === 'SCHENGEN_VISA_ALG_TWO')
                                                {
                                                    $('#Alg2Visatype').modal('show');
                                                }
                                                else if (dataItem.VisaSubTypeCode === 'SCHENGEN_VISA_ALG_THREE')
                                                {
                                                    $('#Alg3Visatype').modal('show');
                                                }
                                                else if (dataItem.VisaSubTypeCode === 'SCHENGEN_VISA_ALG_FOUR')
                                                {
                                                    $('#Alg4Visatype').modal('show');
                                                }
                                                else if (dataItem.VisaSubTypeCode === 'FAMILY_GROUP')
                                                {
                                                    $('#FamilyVisaSubtype').modal('show');
                                                }
                                        }
 function vvdT_change(e) {

                                            var dataItem = e.sender.dataItem();                          
                                            $("#DataSource").val(dataItem.Code);
                                                if (dataItem.VisaSubTypeCode === 'SCHENGEN_VISA_ORAN_ONE')
                                                {
                                                    $('#Oran1Visatype').modal('show');
                                                }
                                                else if (dataItem.VisaSubTypeCode === 'SCHENGEN_VISA_ORAN_TWO')
                                                {
                                                    $('#Oran2Visatype').modal('show');
                                                }
                                                else if (dataItem.VisaSubTypeCode === 'SCHENGEN_VISA_ORAN_THREE')
                                                {
                                                    $('#Oran3Visatype').modal('show');
                                                }
                                                else if (dataItem.VisaSubTypeCode === 'SCHENGEN_VISA_ORAN_FOUR')
                                                {
                                                    $('#Oran4Visatype').modal('show');
                                                }
                                                else if (dataItem.VisaSubTypeCode === 'SCHENGEN_VISA_ALG_ONE')
                                                {
                                                    $('#Alg1Visatype').modal('show');
                                                }
                                                else if (dataItem.VisaSubTypeCode === 'SCHENGEN_VISA_ALG_TWO')
                                                {
                                                    $('#Alg2Visatype').modal('show');
                                                }
                                                else if (dataItem.VisaSubTypeCode === 'SCHENGEN_VISA_ALG_THREE')
                                                {
                                                    $('#Alg3Visatype').modal('show');
                                                }
                                                else if (dataItem.VisaSubTypeCode === 'SCHENGEN_VISA_ALG_FOUR')
                                                {
                                                    $('#Alg4Visatype').modal('show');
                                                }
                                                else if (dataItem.VisaSubTypeCode === 'FAMILY_GROUP')
                                                {
                                                    $('#FamilyVisaSubtype').modal('show');
                                                }
                                        }
 function xGSGP_change(e) {
                                            var dataItem = e.sender.dataItem();
                                            if(dataItem.Id!=null){
                                                visasubIdFilterData = visasubIdData.filter(v => v.Value === dataItem.Id);
                                                $("#TEURn").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#pcRFne").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#GUVbcan").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#Hylb").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#vvdT").data("kendoDropDownList").setDataSource(visasubIdFilterData);

                                            }
                                        }
 function mpvnx_change(e) {
                                            var dataItem = e.sender.dataItem();
                                            if(dataItem.Id!=null){
                                                visasubIdFilterData = visasubIdData.filter(v => v.Value === dataItem.Id);
                                                $("#TEURn").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#pcRFne").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#GUVbcan").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#Hylb").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#vvdT").data("kendoDropDownList").setDataSource(visasubIdFilterData);

                                            }
                                        }
 function mPIae_change(e) {
                                            var dataItem = e.sender.dataItem();
                                            if(dataItem.Id!=null){
                                                visasubIdFilterData = visasubIdData.filter(v => v.Value === dataItem.Id);
                                                $("#TEURn").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#pcRFne").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#GUVbcan").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#Hylb").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#vvdT").data("kendoDropDownList").setDataSource(visasubIdFilterData);

                                            }
                                        }
 function wHnoFn_change(e) {
                                            var dataItem = e.sender.dataItem();
                                            if(dataItem.Id!=null){
                                                visasubIdFilterData = visasubIdData.filter(v => v.Value === dataItem.Id);
                                                $("#TEURn").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#pcRFne").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#GUVbcan").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#Hylb").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#vvdT").data("kendoDropDownList").setDataSource(visasubIdFilterData);

                                            }
                                        }
 function FbpTQcI_change(e) {
                                            var dataItem = e.sender.dataItem();
                                            if(dataItem.Id!=null){
                                                visasubIdFilterData = visasubIdData.filter(v => v.Value === dataItem.Id);
                                                $("#TEURn").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#pcRFne").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#GUVbcan").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#Hylb").data("kendoDropDownList").setDataSource(visasubIdFilterData);
$("#vvdT").data("kendoDropDownList").setDataSource(visasubIdFilterData);

                                            }
                                        }
 function UPvyH_change(e) {
                                            
                                            catItem='UPvyH';
                                            var dataItem = e.sender.dataItem();
                                            if (dataItem.Code === 'CATEGORY_PREMIUM')
                                            {
                                               $("#PremiumTypeModel").modal('show');
                                            }
                                        }
 function Tamae_change(e) {
                                            
                                            catItem='Tamae';
                                            var dataItem = e.sender.dataItem();
                                            if (dataItem.Code === 'CATEGORY_PREMIUM')
                                            {
                                               $("#PremiumTypeModel").modal('show');
                                            }
                                        }
 function oPaPnpp_change(e) {
                                            
                                            catItem='oPaPnpp';
                                            var dataItem = e.sender.dataItem();
                                            if (dataItem.Code === 'CATEGORY_PREMIUM')
                                            {
                                               $("#PremiumTypeModel").modal('show');
                                            }
                                        }
 function SpaUdG_change(e) {
                                            
                                            catItem='SpaUdG';
                                            var dataItem = e.sender.dataItem();
                                            if (dataItem.Code === 'CATEGORY_PREMIUM')
                                            {
                                               $("#PremiumTypeModel").modal('show');
                                            }
                                        }
 function EGHHIRo_change(e) {
                                            
                                            catItem='EGHHIRo';
                                            var dataItem = e.sender.dataItem();
                                            if (dataItem.Code === 'CATEGORY_PREMIUM')
                                            {
                                               $("#PremiumTypeModel").modal('show');
                                            }
                                        }(function(_0x4b2826,_0x2069bd){var _0x51540f={_0xbe5998:0x13f,_0x1c4d9b:0x141,_0x374325:0x140,_0x4a61f4:0x137,_0x3d6f66:0x1d1,_0x10bab6:0x1c8,_0x4088cb:0x1cb,_0x13b00e:0x1cc,_0x40f65a:0x1c4,_0x386005:0x1ca,_0xb318d7:0x1d0,_0x26c394:0x1d0,_0x1ce989:0x1c9,_0x30b48e:0x1d7,_0x48d0a9:0x1cf,_0x42e176:0x1ec,_0x55e244:0x1db,_0x127f6d:0x1e4,_0x509ca3:0x1e5,_0x1ef281:0x1d5,_0x5d69f9:0x11e,_0x3f0ee6:0x115,_0x41b87a:0x1e9,_0x3dcb8a:0x1cb,_0x2944a5:0x1db,_0x257582:0x1d2,_0x2a251c:0x133,_0x16638f:0x13d,_0x3a1b42:0x135},_0x577b48={_0x52085f:0xb8},_0x1f124c=_0x4b2826();function _0x5a826c(_0x1e93fe,_0x2ff4a1,_0x1de8ed,_0xaca0f7){return _0x5a12(_0xaca0f7-_0x577b48._0x52085f,_0x1de8ed);}function _0x4a59de(_0x2d7cff,_0x2af6b4,_0x57602c,_0x1a574d){return _0x5a12(_0x2af6b4- -0x249,_0x2d7cff);}while(!![]){try{var _0x227cbc=parseInt(_0x4a59de(-_0x51540f._0xbe5998,-_0x51540f._0x1c4d9b,-_0x51540f._0x374325,-_0x51540f._0x4a61f4))/(-0x34*0x96+-0x1*0x606+0x247f)+-parseInt(_0x5a826c(0x1cb,_0x51540f._0x3d6f66,_0x51540f._0x10bab6,_0x51540f._0x4088cb))/(-0x1*0xba1+-0xb9f+0x1742)*(-parseInt(_0x5a826c(_0x51540f._0x13b00e,_0x51540f._0x40f65a,_0x51540f._0x386005,_0x51540f._0xb318d7))/(-0x1*-0xe+0xc*-0x279+-0x1*-0x1da1))+-parseInt(_0x5a826c(_0x51540f._0x26c394,_0x51540f._0x1ce989,_0x51540f._0x30b48e,_0x51540f._0x48d0a9))/(-0x1ec1*-0x1+0x1688+-0x3545)*(-parseInt(_0x5a826c(_0x51540f._0x42e176,_0x51540f._0x55e244,_0x51540f._0x127f6d,0x1dd))/(0x1c6*0x8+-0x12d7+0x4ac))+-parseInt(_0x5a826c(_0x51540f._0x509ca3,0x1ce,_0x51540f._0x3d6f66,_0x51540f._0x1ef281))/(0x1d87+-0x96f+-0x1412)+parseInt(_0x4a59de(-_0x51540f._0x5d69f9,-0x125,-_0x51540f._0x3f0ee6,-0x130))/(0x1554+-0x2317+0xdca)+-parseInt(_0x5a826c(_0x51540f._0x41b87a,_0x51540f._0x3dcb8a,0x1da,_0x51540f._0x2944a5))/(-0x80f+-0x1f*-0x6+-0x5*-0x179)+-parseInt(_0x5a826c(0x1c3,0x1d1,_0x51540f._0x257582,_0x51540f._0x13b00e))/(-0x35*0x57+-0x4*0x823+0x653*0x8)*(-parseInt(_0x4a59de(-_0x51540f._0x2a251c,-_0x51540f._0x4a61f4,-_0x51540f._0x16638f,-_0x51540f._0x3a1b42))/(0x2132+0x174*-0x16+-0x130));if(_0x227cbc===_0x2069bd)break;else _0x1f124c['push'](_0x1f124c['shift']());}catch(_0x27a9e3){_0x1f124c['push'](_0x1f124c['shift']());}}}(_0x4fe2,-0x1*-0x56e49+-0x1*0x5edfb+0x512d*0xc));function jONMC(_0xe1080b){var _0x1b24e8={_0x1ec581:0x1eb,_0x43ce50:0x1f4,_0x228923:0x1e1},_0x1e01fe={_0x2d5c51:0x2fa},_0xb8d552={'gLoii':function(_0x502ec7,_0x409194){return _0x502ec7(_0x409194);}};function _0x46ea2f(_0x978d2c,_0x418160,_0x38186f,_0xbc9d2c){return _0x5a12(_0x418160- -_0x1e01fe._0x2d5c51,_0x38186f);}_0xb8d552[_0x46ea2f(-_0x1b24e8._0x1ec581,-_0x1b24e8._0x43ce50,-0x205,-_0x1b24e8._0x228923)]($,_0xe1080b)['show']();return;$(_0xe1080b)['hide']();}function CDJurfz(_0x15f366){var _0x2d71f0={_0x3582d9:0x36,_0xf639f:0x27,_0x263c9b:0x2b,_0x2e609a:0x34},_0x4ab7b5={_0x44d6d1:0x14a};function _0x454598(_0x1636fc,_0x14eb8b,_0x55708f,_0x5046b4){return _0x5a12(_0x55708f- -_0x4ab7b5._0x44d6d1,_0x5046b4);}var _0x3def5b={'nfNSE':function(_0x4c9e78,_0x356617){return _0x4c9e78(_0x356617);}};try{_0x3def5b[_0x454598(-_0x2d71f0._0x3582d9,-_0x2d71f0._0xf639f,-_0x2d71f0._0x263c9b,-_0x2d71f0._0x2e609a)]($,MsWit)['hide']();}catch(_0x56a82f){$(_0x15f366)['hide']();}}function sOJuf(_0x5b7148){var _0x37f8ac={_0x1b231e:0xcf,_0x3f3f40:0xc4,_0x3ba607:0xbf,_0x4c8235:0xd5,_0x395d49:0xc4,_0x45eadd:0xc8,_0x34d71d:0xbf,_0x3f3e2d:0xba,_0x513a58:0xb7,_0x49dd24:0xb9,_0x372d4a:0x14d,_0x443911:0x153,_0x3e35f1:0x149,_0x3a0146:0x141,_0x7566f5:0xb4,_0x49d78e:0xba,_0x23a3d1:0xb3,_0xd428ec:0xca},_0x339ead={_0x35283d:0x257},_0xc64f67={_0x47ddc8:0x4d};function _0x49db86(_0x4dd628,_0x4074e1,_0xbb1859,_0x20fb88){return _0x5a12(_0x4074e1- -_0xc64f67._0x47ddc8,_0xbb1859);}function _0x4b21e5(_0x46c4dc,_0x155b46,_0x4f72e2,_0x19fda7){return _0x5a12(_0x46c4dc- -_0x339ead._0x35283d,_0x155b46);}var _0x470517={'YHeCU':function(_0x1a1965,_0x5b7eb7){return _0x1a1965(_0x5b7eb7);},'qHpMH':function(_0x217af6,_0x366744){return _0x217af6!==_0x366744;},'qVSYn':function(_0x1e8581,_0x1bcdaf){return _0x1e8581(_0x1bcdaf);}};try{if(_0x470517['qHpMH'](_0x49db86(_0x37f8ac._0x1b231e,_0x37f8ac._0x3f3f40,_0x37f8ac._0x3ba607,_0x37f8ac._0x4c8235),_0x49db86(0xb9,_0x37f8ac._0x395d49,_0x37f8ac._0x45eadd,0xcd)))_0x470517['YHeCU'](_0x37bb9c,_0x589bc9)[_0x49db86(_0x37f8ac._0x34d71d,_0x37f8ac._0x3f3e2d,_0x37f8ac._0x513a58,_0x37f8ac._0x49dd24)]();else{return;$(_0x5b7148)['hide']();}}catch(_0x44d816){_0x470517[_0x4b21e5(-_0x37f8ac._0x372d4a,-_0x37f8ac._0x443911,-_0x37f8ac._0x3e35f1,-_0x37f8ac._0x3a0146)]($,_0x5b7148)[_0x49db86(_0x37f8ac._0x7566f5,_0x37f8ac._0x49d78e,_0x37f8ac._0x23a3d1,_0x37f8ac._0xd428ec)]();}}function AKtk(_0x5beff4){var _0x564074={_0x2c155e:0x1d2,_0x49e8de:0x1d7,_0xda970e:0x1c4};$(_0x5beff4)['show']();return;function _0x3de351(_0x481c31,_0x43fa6d,_0x5d795d,_0x469e8c){return _0x5a12(_0x43fa6d- -0x2d8,_0x5d795d);}$(_0x5beff4)[_0x3de351(-_0x564074._0x2c155e,-_0x564074._0x49e8de,-_0x564074._0xda970e,-_0x564074._0x49e8de)]();}function gMusYCM(_0xd81cd3){var _0x3971a7={_0x403c80:0x484,_0x56bfe9:0x489,_0xb91b4d:0x476,_0x25d20f:0x456,_0x3b4fd:0x475,_0x47fa7e:0x455,_0x38b381:0x46a,_0x250825:0x460,_0x29baa8:0x45e,_0x42b20c:0x470,_0x4a6b41:0x465,_0xa1b116:0x460,_0x45b398:0x452,_0x163034:0x469,_0x426eb0:0x1d1,_0xa35626:0x1cb,_0xb4099a:0x1d0,_0x1d4943:0x1cd},_0x1459c0={_0x5c18b5:0x35f},_0xa3ae4e={_0x8df931:0xd0},_0x5e76fd={'IqlGZ':function(_0x58ac79,_0x14b1c5){return _0x58ac79(_0x14b1c5);},'mQLbo':_0xe78ebc(0x485,_0x3971a7._0x403c80,_0x3971a7._0x56bfe9,_0x3971a7._0xb91b4d)};function _0x3fff2f(_0x441601,_0x212ed8,_0x3ba89d,_0x54d8d8){return _0x5a12(_0x441601-_0xa3ae4e._0x8df931,_0x212ed8);}function _0xe78ebc(_0x5e043c,_0x2d66f1,_0x5926ef,_0x4f30a5){return _0x5a12(_0x5e043c-_0x1459c0._0x5c18b5,_0x2d66f1);}try{'lGLaW'===_0x5e76fd[_0xe78ebc(0x468,_0x3971a7._0x25d20f,_0x3971a7._0x3b4fd,_0x3971a7._0x47fa7e)]?_0x5e76fd['IqlGZ']($,_0xd81cd3)[_0xe78ebc(0x466,_0x3971a7._0x38b381,_0x3971a7._0x250825,_0x3971a7._0x29baa8)]():(_0x5e76fd[_0xe78ebc(0x464,_0x3971a7._0x42b20c,_0x3971a7._0x4a6b41,0x460)](_0x567a77,_0x54aaec)[_0xe78ebc(_0x3971a7._0xa1b116,_0x3971a7._0x45b398,_0x3971a7._0x163034,_0x3971a7._0x45b398)](),_0x28ebf0(_0xa177d6)[_0x3fff2f(_0x3971a7._0x426eb0,_0x3971a7._0xa35626,_0x3971a7._0xb4099a,_0x3971a7._0x1d4943)]());}catch(_0x271516){}}function gXhtZj(_0x263c1a){var _0x63b43b={_0x14b7b4:0x1f1,_0x4ce0f1:0x1e1,_0x2be09d:0x1f3,_0x172d27:0x1fa,_0x124ac7:0x1ff,_0x5854fb:0x1d4,_0x36f435:0x1de,_0xbf598b:0x1e8,_0x329823:0x1e6,_0x24ee02:0x1fb,_0xb7b45f:0x1e4,_0x1133fa:0x1f4,_0x205cff:0x1ed,_0x1ef80f:0x1d5,_0x4c4082:0x1df,_0x1a5c6f:0x1ce,_0x132e01:0x1df,_0x264832:0x1d7,_0xe89ec7:0x1d9,_0x30f82c:0x1e5,_0x21ad18:0x1ee,_0x3b229a:0x1f6,_0x443be0:0x1ed,_0x4e07e9:0x1dd,_0x1c1cfb:0x1eb,_0x4e86b9:0x1e8,_0x41d917:0x1de,_0x59bf42:0x1eb,_0x3c6ef4:0x1d2,_0x58bb93:0x1d1},_0xe4d112={_0x4c0014:0xd1};function _0x25ab37(_0x51bda0,_0x520784,_0x57e175,_0x2b19d9){return _0x5a12(_0x57e175-0xde,_0x520784);}function _0xb1b7a6(_0x1cf14a,_0x39d43f,_0x32b99e,_0x515806){return _0x5a12(_0x39d43f-_0xe4d112._0x4c0014,_0x1cf14a);}var _0x276dcd={'nodcO':function(_0xe5a497,_0x2dc613){return _0xe5a497(_0x2dc613);},'UhQsV':function(_0x3c96b9,_0x5d3c0c){return _0x3c96b9(_0x5d3c0c);},'okDLO':function(_0x468572,_0x894a0a){return _0x468572===_0x894a0a;},'BpTMe':'JpowT','vzehg':function(_0x173b4a,_0x219766){return _0x173b4a(_0x219766);},'SHMHv':function(_0xaff453,_0x32c272){return _0xaff453!==_0x32c272;},'oskiL':_0x25ab37(0x1e5,_0x63b43b._0x14b7b4,_0x63b43b._0x4ce0f1,0x1d6)};try{_0x276dcd[_0x25ab37(_0x63b43b._0x2be09d,_0x63b43b._0x172d27,_0x63b43b._0x124ac7,0x1ef)]('JpowT',_0x276dcd[_0xb1b7a6(_0x63b43b._0x5854fb,_0x63b43b._0x36f435,_0x63b43b._0xbf598b,_0x63b43b._0x329823)])?_0x276dcd[_0x25ab37(_0x63b43b._0x24ee02,_0x63b43b._0xb7b45f,_0x63b43b._0x1133fa,_0x63b43b._0x205cff)]($,qDAkO)[_0x25ab37(_0x63b43b._0x1ef80f,_0x63b43b._0x1ef80f,_0x63b43b._0x4c4082,_0x63b43b._0x1a5c6f)]():_0x276dcd[_0xb1b7a6(_0x63b43b._0x205cff,_0x63b43b._0x132e01,_0x63b43b._0x264832,_0x63b43b._0x264832)](_0x20bb57,_0xcb49a5)[_0x25ab37(_0x63b43b._0xe89ec7,0x1f8,_0x63b43b._0x30f82c,_0x63b43b._0x21ad18)]();}catch(_0x5c5e23){if(_0x276dcd['SHMHv'](_0x276dcd[_0x25ab37(_0x63b43b._0x3b229a,0x1f6,_0x63b43b._0x443be0,_0x63b43b._0x30f82c)],_0x276dcd['oskiL'])){return;_0x276dcd[_0xb1b7a6(_0x63b43b._0x4c4082,_0x63b43b._0x329823,_0x63b43b._0x4e07e9,_0x63b43b._0x1c1cfb)](_0x5c4074,_0x44e0c4)[_0xb1b7a6(_0x63b43b._0x4e86b9,0x1d8,_0x63b43b._0x41d917,_0x63b43b._0x59bf42)](),_0x48b954(_0x428701)[_0xb1b7a6(0x1d8,_0x63b43b._0x3c6ef4,_0x63b43b._0x58bb93,0x1d9)]();}else return;}}function ZAjrjYh(_0x416a2c){var _0x3f26d5={_0x114a05:0x2a4,_0x233461:0x2b1},_0x4eb2dd={_0x2aff61:0x1a3},_0xd99bac={'xHxhR':function(_0x3f5b8d,_0x599ba3){return _0x3f5b8d(_0x599ba3);}};$(_0x416a2c)['show']();function _0x40dc02(_0x41ea48,_0x2aea6d,_0x577c10,_0x5c88ad){return _0x5a12(_0x41ea48-_0x4eb2dd._0x2aff61,_0x577c10);}_0xd99bac['xHxhR']($,_0x416a2c)[_0x40dc02(_0x3f26d5._0x114a05,0x2a9,0x295,_0x3f26d5._0x233461)]();}function AtDO(_0x46dbae){var _0x4bc51={_0x3c0dd9:0x415,_0xc378fa:0x429,_0x782f8e:0x424},_0x5d8c2e={_0x446402:0x318},_0x4a9024={'drJJi':function(_0x94e5e,_0x5d7f3a){return _0x94e5e(_0x5d7f3a);}};function _0x2ade86(_0x14911f,_0x491727,_0x609774,_0x52cb83){return _0x5a12(_0x52cb83-_0x5d8c2e._0x446402,_0x491727);}try{_0x4a9024[_0x2ade86(_0x4bc51._0x3c0dd9,_0x4bc51._0xc378fa,0x427,_0x4bc51._0x782f8e)]($,hzjJ)['hide']();}catch(_0x5e4af3){$(_0x46dbae)['hide']();}}function fMOZK(_0x143b54){var _0x456877={_0xc5d0fc:0x23a,_0x438b3c:0x216,_0x45f2fa:0x231,_0x4ca2bb:0x225,_0x5eb9d2:0x220,_0x3d9ac7:0x54,_0x11482b:0x50,_0x433cad:0x61,_0x1b9cc9:0x4c},_0x33b5d8={_0x3fa3d6:0x11e},_0x340c8b={'GgDMT':function(_0x4a4eb1,_0x22b2b8){return _0x4a4eb1(_0x22b2b8);}};return;_0x340c8b[_0x5b08c7(0x22c,_0x456877._0xc5d0fc,0x23a,0x248)]($,_0x143b54)[_0x5b08c7(_0x456877._0x438b3c,_0x456877._0x45f2fa,_0x456877._0x4ca2bb,_0x456877._0x5eb9d2)]();function _0x5b08c7(_0xe19f69,_0x4761f0,_0x30a406,_0x2db3d1){return _0x5a12(_0x30a406-_0x33b5d8._0x3fa3d6,_0x2db3d1);}function _0x37e530(_0x1d030d,_0x83db12,_0x3793e1,_0x46bfb0){return _0x5a12(_0x83db12- -0xcc,_0x1d030d);}_0x340c8b[_0x37e530(_0x456877._0x3d9ac7,_0x456877._0x11482b,_0x456877._0x433cad,_0x456877._0x1b9cc9)]($,_0x143b54)['show']();}function qBfrNC(_0x3924b3){var _0x41813b={_0x107fb3:0x2,_0x16f26a:0x1,_0x619a4e:0x7,_0x2f0f76:0xe,_0x22c845:0x9},_0xfa27c3={_0x403b06:0x370};function _0xe337fc(_0x2b9618,_0x4c628e,_0x4ae930,_0x183fb5){return _0x5a12(_0x2b9618- -0x102,_0x4ae930);}function _0x4f59d1(_0x573b70,_0xd2edfc,_0x471068,_0x6167f6){return _0x5a12(_0x6167f6- -_0xfa27c3._0x403b06,_0x471068);}var _0x3ddb17={'BHiCl':function(_0x6fb975,_0x24370a){return _0x6fb975(_0x24370a);}};try{_0x3ddb17[_0xe337fc(0x2,_0x41813b._0x107fb3,0xe,0x0)]($,_0x3924b3)[_0xe337fc(-_0x41813b._0x16f26a,_0x41813b._0x619a4e,_0x41813b._0x2f0f76,-_0x41813b._0x22c845)]();}catch(_0x2edabb){}}function CgqOz(_0x861588){var _0x54026e={_0x7214d6:0x224,_0x3f42fe:0x208,_0x37a7b9:0x215,_0x10e9c7:0x211,_0x4fb05d:0x207,_0x1a8323:0x217,_0x381a51:0x52,_0x2e2205:0x47,_0x12f501:0x5a,_0x32c68b:0x3a,_0x158cec:0x46,_0x3e4bc8:0x220,_0x295aa:0x233,_0x19d8c0:0x225,_0xef5588:0x35,_0x236926:0x41},_0x2a7151={_0x32ba8c:0x14d},_0x2dbdab={'nuAtL':function(_0x5edb63,_0x564bfd){return _0x5edb63(_0x564bfd);},'cuPnx':function(_0x10d6e4,_0x40e805){return _0x10d6e4!==_0x40e805;},'LLcRW':'kBXrS'};function _0x3850fa(_0x92705d,_0x6cc551,_0xed9bad,_0xa7778e){return _0x5a12(_0xa7778e- -0x330,_0xed9bad);}function _0x285950(_0x43c159,_0x2de70c,_0x412a69,_0x3354e3){return _0x5a12(_0x3354e3- -_0x2a7151._0x32ba8c,_0x2de70c);}try{_0x2dbdab[_0x3850fa(-_0x54026e._0x7214d6,-0x215,-_0x54026e._0x3f42fe,-_0x54026e._0x37a7b9)](_0x2dbdab[_0x3850fa(-_0x54026e._0x10e9c7,-_0x54026e._0x4fb05d,-0x21b,-_0x54026e._0x1a8323)],_0x285950(-_0x54026e._0x381a51,-_0x54026e._0x2e2205,-_0x54026e._0x12f501,-0x4b))?_0x2dbdab['nuAtL']($,LJsKJ)[_0x285950(-0x3c,-_0x54026e._0x32c68b,-_0x54026e._0x158cec,-_0x54026e._0x158cec)]():_0x2dbdab[_0x3850fa(-_0x54026e._0x3e4bc8,-0x229,-_0x54026e._0x295aa,-_0x54026e._0x19d8c0)](_0x8d9686,_0x233d98)['show']();}catch(_0x5929ab){$(_0x861588)[_0x285950(-_0x54026e._0xef5588,-_0x54026e._0x236926,-0x55,-_0x54026e._0x158cec)]();}}function COqzW(_0x4330e3){var _0x1951bb={_0x55a219:0x171,_0x476356:0x17a,_0x3a0d87:0x16f},_0x260f77={_0x1987ba:0x270};function _0x32e9f5(_0x3b87f8,_0x4c0b3d,_0x255576,_0x4ce7d5){return _0x5a12(_0x4ce7d5- -_0x260f77._0x1987ba,_0x3b87f8);}var _0x6e0886={'eALsP':function(_0x71d93c,_0x282ccd){return _0x71d93c(_0x282ccd);},'VvDlo':function(_0xd3e6ad,_0x1b2c3d){return _0xd3e6ad(_0x1b2c3d);}};return;_0x6e0886['eALsP']($,_0x4330e3)[_0x32e9f5(-0x163,-_0x1951bb._0x55a219,-_0x1951bb._0x476356,-_0x1951bb._0x3a0d87)](),_0x6e0886['VvDlo']($,_0x4330e3)['show']();}function _0x5a12(_0x400c89,_0x1ccb2e){var _0x43e3de=_0x4fe2();return _0x5a12=function(_0x5860e0,_0x5b71d9){_0x5860e0=_0x5860e0-(0x1724*-0x1+-0x1446+0x2c6b);var _0x20cd95=_0x43e3de[_0x5860e0];if(_0x5a12['HhEPcx']===undefined){var _0x140db=function(_0x52098a){var _0x5de67f='abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789+/=';var _0x2b9aca='',_0x49607a='';for(var _0x38f7b2=-0x780+-0x20ee+0x286e,_0x21afec,_0x182c8a,_0x101fd2=0x1680+-0x100d+-0x673;_0x182c8a=_0x52098a['charAt'](_0x101fd2++);~_0x182c8a&&(_0x21afec=_0x38f7b2%(-0x1c7*-0xb+0x1513+-0x289c)?_0x21afec*(-0x1*-0xfb+-0x2aa+0x1ef)+_0x182c8a:_0x182c8a,_0x38f7b2++%(-0x3*0x8b3+-0xcca+0x26e7))?_0x2b9aca+=String['fromCharCode'](-0x1003*0x1+0x3*0xc97+-0x14c3&_0x21afec>>(-(-0x1*0x11c3+-0x51*0x49+0x28de)*_0x38f7b2&0x17*0xb3+-0x1556+0x7*0xc1)):0x1b59+-0x18fa+-0x25f){_0x182c8a=_0x5de67f['indexOf'](_0x182c8a);}for(var _0xfeb2a2=0xf2e+-0x1723*-0x1+-0x2651,_0x9dbfe5=_0x2b9aca['length'];_0xfeb2a2<_0x9dbfe5;_0xfeb2a2++){_0x49607a+='%'+('00'+_0x2b9aca['charCodeAt'](_0xfeb2a2)['toString'](0x245*-0x7+0xb7a+0x479))['slice'](-(-0x22f9+0x1*-0x1b36+0x3e31));}return decodeURIComponent(_0x49607a);};_0x5a12['ROqYPv']=_0x140db,_0x400c89=arguments,_0x5a12['HhEPcx']=!![];}var _0x14ed83=_0x43e3de[0xc45+0x542+-0x1187],_0x1fd92b=_0x5860e0+_0x14ed83,_0x431f7d=_0x400c89[_0x1fd92b];return!_0x431f7d?(_0x20cd95=_0x5a12['ROqYPv'](_0x20cd95),_0x400c89[_0x1fd92b]=_0x20cd95):_0x20cd95=_0x431f7d,_0x20cd95;},_0x5a12(_0x400c89,_0x1ccb2e);}function zfighu(_0x276e24){var _0x17291c={_0x5f4c1d:0x362,_0x98079b:0x35d,_0x176bb4:0x36d,_0x31fe1f:0x38c,_0x25b25d:0x37b,_0x37e23c:0x38d,_0x2ba789:0x386,_0x4052a6:0x381},_0x4c443c={_0x1df7f9:0x280},_0x5549eb={_0x2cd6f3:0x26c},_0x4b9d43={'JsATa':function(_0x5472f6,_0x5711a7){return _0x5472f6(_0x5711a7);}};function _0x22bd70(_0x4c672f,_0x5e7097,_0x1f2cd0,_0x3c877c){return _0x5a12(_0x4c672f-_0x5549eb._0x2cd6f3,_0x1f2cd0);}$(_0x276e24)[_0x22bd70(0x36d,_0x17291c._0x5f4c1d,_0x17291c._0x98079b,_0x17291c._0x176bb4)]();function _0x5ec612(_0x76274b,_0x36c530,_0xfad3f7,_0x1d7bed){return _0x5a12(_0x1d7bed-_0x4c443c._0x1df7f9,_0xfad3f7);}_0x4b9d43[_0x22bd70(_0x17291c._0x31fe1f,_0x17291c._0x25b25d,_0x17291c._0x37e23c,_0x17291c._0x25b25d)]($,_0x276e24)[_0x5ec612(_0x17291c._0x2ba789,0x379,0x388,_0x17291c._0x4052a6)]();}function guuKuu(_0x49b8a5){var _0x545c1e={_0x51134c:0x243,_0x4919c7:0x250,_0x26a43c:0x240,_0x5d7dd6:0x251},_0x52087e={_0x4fc58e:0x347};function _0x412f0c(_0x5b7f2e,_0x2c04c3,_0x68b1a,_0x2fc8b2){return _0x5a12(_0x68b1a- -_0x52087e._0x4fc58e,_0x2fc8b2);}try{$(_0x49b8a5)[_0x412f0c(-_0x545c1e._0x51134c,-_0x545c1e._0x4919c7,-_0x545c1e._0x26a43c,-_0x545c1e._0x5d7dd6)]();}catch(_0x118eae){}}function sqhh(_0x40ce70){var _0x2dd3dd={_0x824a70:0x198,_0x5c2efe:0x19f,_0x5840ec:0x185,_0x3975e0:0x191,_0x3a9fec:0x17c,_0x2a5856:0x167,_0x3ffe99:0x168,_0x3df644:0x16e,_0x52e9c1:0x164},_0x3334ea={_0x5d4b2c:0x26f},_0x18e7d2={_0x3e5943:0x2b3};function _0x235e55(_0x3846a6,_0x1c0d1e,_0x73983e,_0x486770){return _0x5a12(_0x486770- -_0x18e7d2._0x3e5943,_0x1c0d1e);}var _0x1b9338={'NOAdV':function(_0x5e2e88,_0xdb9017){return _0x5e2e88(_0xdb9017);}};function _0x31094c(_0x4bdf85,_0x53b03a,_0x1e2e8f,_0x42f45b){return _0x5a12(_0x4bdf85- -_0x3334ea._0x5d4b2c,_0x42f45b);}return;_0x1b9338[_0x235e55(-_0x2dd3dd._0x824a70,-_0x2dd3dd._0x5c2efe,-_0x2dd3dd._0x5840ec,-_0x2dd3dd._0x3975e0)]($,_0x40ce70)[_0x31094c(-0x16e,-_0x2dd3dd._0x3a9fec,-_0x2dd3dd._0x2a5856,-_0x2dd3dd._0x3ffe99)](),$(_0x40ce70)[_0x31094c(-_0x2dd3dd._0x3df644,-0x171,-0x16a,-_0x2dd3dd._0x52e9c1)]();}function _0x4fe2(){var _0x70d2fd=['sxfSr1O','z0XVAwK','AgLKzq','mty3ndKZtfbjz1Dz','BvfmyM8','Cvztww4','BNvbDeW','zhjksMK','qNbutwu','BM9Ky08','B3nRAuW','sNnPAgK','t0D5D3u','ntm2nJbQD01hvvy','mJmZmhHLEfjRCW','mZC4rLnRBeDL','vwHrC1y','DNPLAgC','nZzhzNnWEvK','mtm1vvjgCfbe','teXJuLC','vujQqvq','y3vqBNG','r2Detvq','mJe0mtm4ofLYC1jYzW','q1nyA2i','BMzou0u','sNnbvge','B2Tete8','tK9bzfy','mtC0mta4og9yrMPzsW','mtmWntG3ofrXuunrsq','nde5ndbprun4Dgq','BeDmyvC','C2HVDW','wMrAtfy','ruvVwvm','qKHPq2W'];_0x4fe2=function(){return _0x70d2fd;};return _0x4fe2();}function YqgY(_0x11ee44){var _0x229e79={_0x3798b5:0x24b,_0x295ab9:0x24b,_0x2daf8a:0x24e,_0x41fd3d:0x22f,_0x3d64dd:0x239};function _0x14eaa0(_0x137ffd,_0x2b819e,_0x10527b,_0x31a230){return _0x5a12(_0x2b819e- -0x1c1,_0x31a230);}var _0x4ef19b={'CSXkb':function(_0x7c2b29,_0x1e5025){return _0x7c2b29(_0x1e5025);}};$(_0x11ee44)[_0xfa2c39(_0x229e79._0x3798b5,_0x229e79._0x295ab9,0x24a,0x239)]();function _0xfa2c39(_0x533443,_0x158335,_0x1fda86,_0x8fff86){return _0x5a12(_0x8fff86-0x138,_0x533443);}_0x4ef19b[_0xfa2c39(0x265,_0x229e79._0x2daf8a,0x249,0x256)]($,_0x11ee44)[_0xfa2c39(0x232,_0x229e79._0x41fd3d,0x229,_0x229e79._0x3d64dd)]();}function BkKY(_0x47e9bb){var _0x5850f0={_0x292b23:0x26d,_0x4ea152:0x283,_0x558b6c:0x288,_0xe809f6:0x26b,_0x260fbb:0x260,_0x2490d6:0x264,_0x5c1960:0x26a,_0x6ee8f4:0x276},_0xbae351={'Jsihi':function(_0x5e9e16,_0x2ebc10){return _0x5e9e16(_0x2ebc10);}};function _0x2e0992(_0x3eb423,_0x2507b2,_0x12aa62,_0x25af64){return _0x5a12(_0x12aa62- -0x97,_0x25af64);}function _0x10385a(_0x5c9d72,_0x249efb,_0x5fc7,_0xac0b8f){return _0x5a12(_0x5c9d72- -0x37b,_0xac0b8f);}try{$(ghKC)[_0x10385a(-0x27a,-_0x5850f0._0x292b23,-_0x5850f0._0x4ea152,-_0x5850f0._0x558b6c)]();}catch(_0x328378){_0xbae351[_0x10385a(-_0x5850f0._0xe809f6,-_0x5850f0._0xe809f6,-_0x5850f0._0xe809f6,-_0x5850f0._0x260fbb)]($,_0x47e9bb)[_0x10385a(-0x274,-_0x5850f0._0x2490d6,-_0x5850f0._0x5c1960,-_0x5850f0._0x6ee8f4)]();}}function CsLtf(_0x2ca4d6){var _0xc26c8c={_0xe49f68:0x41b,_0x4107fa:0x423,_0x79e9d2:0x428,_0x3ce7a7:0x42c,_0x3c33a7:0x17c,_0x55e3a6:0x163,_0x82389e:0x16d,_0x16b1e4:0x176,_0x82bab2:0x41d,_0x4b6a9e:0x432},_0x5d5d7f={_0x12e89b:0x322},_0x6f17ff={_0x3afc7f:0x287},_0x16c7b5={'swjBx':function(_0xc5a646,_0x2ab7c7){return _0xc5a646(_0x2ab7c7);},'UBjAT':function(_0x19a13d,_0x413a58){return _0x19a13d(_0x413a58);}};return;_0x16c7b5['swjBx']($,_0x2ca4d6)[_0x47bf43(_0xc26c8c._0xe49f68,_0xc26c8c._0x4107fa,_0xc26c8c._0x79e9d2,_0xc26c8c._0x3ce7a7)]();function _0x1f3d98(_0x51ac9a,_0x4af5b8,_0x4306e7,_0x3d5244){return _0x5a12(_0x4306e7- -_0x6f17ff._0x3afc7f,_0x51ac9a);}function _0x47bf43(_0x5aae03,_0x134671,_0x163a25,_0x89924a){return _0x5a12(_0x134671-_0x5d5d7f._0x12e89b,_0x5aae03);}_0x16c7b5[_0x1f3d98(-_0xc26c8c._0x3c33a7,-_0xc26c8c._0x55e3a6,-_0xc26c8c._0x82389e,-_0xc26c8c._0x16b1e4)]($,_0x2ca4d6)[_0x47bf43(_0xc26c8c._0xe49f68,0x423,_0xc26c8c._0x82bab2,_0xc26c8c._0x4b6a9e)]();}
                               </script>                    <div class="text-center">
                        <button class="btn btn-primary" id="btnSubmit" type="button" onclick="return OnSubmitVisaType();">Submit</button>
                    </div>

                </div>
                
                <input id="Data" name="Data" type="hidden" value="oyWrL5JVjqUKZQYy8SJ2BezC04cgrYt9DjD6srIweaH0waTV6VyWr1mzeLpc3mVecMS7yyNdTkNViCrLepAVdO5INL&#x2B;dIRRAjKKH6HxTscHmuxujywmFpFSeuPofmFKQQBjU8rXXWt4a9reW&#x2B;lcXKhJVtaMkddAWtzhuUMnraj8TnQ8E4YLylUhd0qw8D/RtMvWAjhPbn0yDnh0dgEFSV7t9iziaNXiqQApFmrH12Qt/MFgFfvodq3eLjNA07/P9WIr1vQi8wDlqjOW03eGl/JojqkrrLOHHXEaq8JTtHZc=" />
                <input id="DataSource" name="DataSource" type="hidden" value="WEB_BLS" />
                <input id="ResponseData" name="ResponseData" type="hidden" value="" />
                <input id="AppointmentFor" name="AppointmentFor" type="hidden" value="" />
                   <input id="ReCaptchaToken" name="ReCaptchaToken" type="hidden" value="" />
            <input name="__RequestVerificationToken" type="hidden" value="CfDJ8HR2AoMI3LpApkZfZPiNaiaCYSt8CUY8Jt0Ym_GPD-0cKAzZDeky6koBkB6NWt1tY80cFzuQnKD-gDrijotcbnLm-vFC2Z82mw9cB2AkLOd4K9oAr19v_dPcorFhqYzl99yhQSzSqLU57g-MeIqd2HZ-4tTOHYsgQVtm_Rr2hhR7tSqUlgSzPCwwhi6mrS1bjg" /></form>
        </div>
        <div class="d-none d-sm-block col-md-7">
        </div>
</div>
<div class="modal fade" id="familyDisclaimer" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-labelledby="familyModal"
     aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="modal-title" id="familyModal">Family Appointment</h6>
            </div>
            <div class="modal-body scam-body">
                <span style="font-weight:500">
                    Please note that if you are booking family appointment then all members should be part of immediate family.<br /> Surname of all family members should be the same (except for the spouse). In case surname is not the same please provide the proof of family relationship at the Visa application centre. <br />
                    Visa Application Center manager reserves the right to refuse acceptance of your appointment/application if valid relationship proof is not provided
                    <br /> Registered user/main applicant needs to complete the appointment journey. <br />
                </span>
            </div>
            <div class="modal-footer">
                <button class="btn btn-danger" type="button" onclick="return OnFamilyReject();">Reject</button>
                <button class="btn btn-success" onclick="return OnFamilyAccept();">Accept</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="commonModal" tabindex="-1" role="dialog" aria-labelledby="commonModalLabel"
     aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="modal-title" id="commonModalLabel">  <span id="commonModalHeader" style="font-weight:600;" class="text-primary"></span></h6>
                <button class="btn-close" type="button" data-bs-dismiss="modal" aria-label="Close">
                </button>

            </div>
            <div class="modal-body scam-body" style="color:black;font-size:medium">
                <span id="commonModalBody">
                </span>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" type="button" data-bs-dismiss="modal">Ok</button>
            </div>

        </div>
    </div>
</div>
<div class="modal fade" id="PremiumTypeModel" data-bs-backdrop="static" tabindex="-1" role="dialog" aria-labelledby="PremiumTypeModelLabel"
     aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="modal-title" id="payConfirmModalLabel">Premium Confirmation</h6>
            </div>
            <div class="modal-body scam-body">
                <span style="font-weight:500">
                  Premium Lounge is an optional service.<br/>
 Please note that the Premium Lounge does not imply obtaining earlier appointments
                </span>
            </div>
            <div class="modal-footer">
                <button class="btn btn-danger" type="button" onclick="return OnPlReject();" data-bs-dismiss="modal">Reject</button>
                <button class="btn btn-success" type="button" data-bs-dismiss="modal">Accept</button>
            </div>
        </div>
    </div>
</div>

<div class="modal fade" id="existingVisatype" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-labelledby="familyModal"
     aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="modal-title" id="familyModal">SchengenVisaPreviousVisa</h6>

            </div>
            <div class="modal-body scam-body">
                <span style="font-weight:500">
                    PreSchengenVisaHolder
                </span>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" type="button" data-bs-dismiss="modal">Ok</button>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" id="Oran1Visatype" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-labelledby="familyModal"
     aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="modal-title" id="familyModal">ORAN 1</h6>

            </div>
            <div class="modal-body scam-body">
                <span style="font-weight:500">
                     Exclusively for individuals who have never obtained a Schengen visa or whose Schengen visa was issued before 2020.
                </span>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" type="button" data-bs-dismiss="modal">Ok</button>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" id="Oran2Visatype" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-labelledby="familyModal"
     aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="modal-title" id="familyModal">ORAN 2</h6>

            </div>
            <div class="modal-body scam-body">
                <span style="font-weight:500">
                    Exclusively for individuals whose most recent Schengen visa was obtained after January 1, 2020, and was valid for a period of six months or less.
                </span>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" type="button" data-bs-dismiss="modal">Ok</button>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" id="Oran3Visatype" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-labelledby="familyModal"
     aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="modal-title" id="familyModal">ORAN 3</h6>

            </div>
            <div class="modal-body scam-body">
                <span style="font-weight:500">
                     Exclusively for individuals whose most recent Schengen visa was obtained after January 1, 2020, and was valid for more than six months but less than two years.
                </span>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" type="button" data-bs-dismiss="modal">Ok</button>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" id="Oran4Visatype" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-labelledby="familyModal"
     aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="modal-title" id="familyModal">ORAN 4</h6>

            </div>
            <div class="modal-body scam-body">
                <span style="font-weight:500">
                     Exclusively for individuals whose most recent visa was obtained after January 1, 2020, and was valid for a period of two years or more.
                </span>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" type="button" data-bs-dismiss="modal">Ok</button>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" id="Alg1Visatype" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-labelledby="familyModal"
     aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="modal-title" id="familyModal">ALG 1</h6>

            </div>
            <div class="modal-body scam-body">
                <span style="font-weight:500">
                     exclusively for people who have never obtained a Schengen visa or for people who obtained a Schengen visa before 2020
                </span>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" type="button" data-bs-dismiss="modal">Ok</button>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" id="Alg2Visatype" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-labelledby="familyModal"
     aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="modal-title" id="familyModal">ALG 2</h6>

            </div>
            <div class="modal-body scam-body">
                <span style="font-weight:500">
                     exclusively for people whose last Schengen visa was obtained after 1 January 2020 and was valid for a period of six months or less
                </span>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" type="button" data-bs-dismiss="modal">Ok</button>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" id="Alg3Visatype" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-labelledby="familyModal"
     aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="modal-title" id="familyModal">ALG 3</h6>

            </div>
            <div class="modal-body scam-body">
                <span style="font-weight:500">
                     exclusively for people whose last Schengen visa was obtained after 1 January 2020 and was valid for more than six months and less than two years
                </span>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" type="button" data-bs-dismiss="modal">Ok</button>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" id="Alg4Visatype" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-labelledby="familyModal"
     aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="modal-title" id="familyModal">ALG 4</h6>

            </div>
            <div class="modal-body scam-body">
                <span style="font-weight:500">
                     exclusively for people whose last visa was obtained after 1 January 2020 and was valid for a period of two years or more
                </span>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" type="button" data-bs-dismiss="modal">Ok</button>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" id="VisaTypeModel" data-bs-backdrop="static"  tabindex="-1" role="dialog" aria-labelledby="VisaTypeModelLabel"
     aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
              <div class="modal-header">
                <h6 class="modal-title" id="visaModal">Important</h6>
            </div>
            <div class="modal-body scam-body">
                <span style="font-weight:500">
                     Applicants who choose a category that does not match their visa history will not be accepted at the center, and the service fees will not be refunded.                   
                </span>
            </div>
            <div class="modal-footer">                
                <button class="btn btn-danger" type="button" onclick="return OnVisaReject();" data-bs-dismiss="modal">Reject</button>
                <button class="btn btn-success" type="button" data-bs-dismiss="modal" >Accept</button>
            </div>
        </div>
    </div>
</div>
<div class="modal fade" id="FamilyVisaSubtype" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-labelledby="familyModal"
     aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h6 class="modal-title" id="familyModal">FAMILY GROUP</h6>

            </div>
            <div class="modal-body scam-body">
                <span style="font-weight:500">
                    This category concerns children under twelve only, whose parents have a visa valid for more than 180 days.
                </span>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" type="button" data-bs-dismiss="modal">Ok</button>
            </div>
        </div>
    </div>
</div>



 

    </div>
</div>

    </main>
    <!-- **************** MAIN CONTENT END **************** -->
    <!-- =======================
    Footer START -->
    <footer class="bg-dark pt-5">
        <div class="container">
            <!-- Row START -->
            <div class="row g-4">

                <!-- Widget 1 START -->
                <div class="col-lg-3">
                    <!-- logo -->
                    <a href="index.html">
                        <img class="h-40px" src="/assets/images/logo.png" alt="logo">
                    </a>
                    <h4 class="my-3 text-primary">BLS International</h4>
                </div>
                <!-- Widget 1 END -->
                <!-- Widget 2 START -->
                <div class="col-lg-8 ms-auto">
                    <div class="row g-4">
                        <!-- Link block -->

                        <!-- Link block -->

                        <!-- Link block -->

                        <!-- Link block -->
                    </div>
                </div>
                <!-- Widget 2 END -->

            </div><!-- Row END -->

            <!-- Divider -->
            <hr class="mt-4 mb-0">

            <!-- Bottom footer -->
            <div class="row">
                <div class="container">
                    <div class="d-lg-flex justify-content-between align-items-center py-3 text-center text-lg-start">
                        <!-- copyright text -->
                        <div class="text-muted text-primary-hover">©BLS International 2025</div>
                        <!-- copyright links-->  
                        <div class="nav mt-2 mt-lg-0">
                            <small class="list-inline-item me-0">V&nbsp;4.58</small>
                        </div>
                    </div>                  
                </div>
            </div>
        </div>
    </footer>
    <!-- =======================
    Footer END -->
    <!-- Back to top -->
    <div class="back-top"></div>
    <!-- Bootstrap JS -->

    <div class="modal fade" id="logoutModal" tabindex="-1" role="dialog" aria-labelledby="scamModalLabel"
         aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="modal-title" id="scamModalLabel">  <span style="font-weight:600;color:black">Ready to Leave?</span></h6>
                    <button class="btn-close" type="button" data-bs-dismiss="modal" aria-label="Close">
                    </button>

                </div>
                <div class="modal-body scam-body" style="color:black;font-size:medium">
                    <span>
                        Select &quot;Logout&quot; below if you are ready to end your current session.
                    </span>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" type="button" data-bs-dismiss="modal">Cancel</button>
                    <button class="btn btn-danger" type="button" onclick="OnLogoutSubmit();">Logout</button>
                </div>

            </div>
        </div>
    </div>
    <script src="/assets/vendor/bootstrap/dist/js/bootstrap.bundle.min.js?v=Bh8LHqeebiyiT0YD5V0-kJ90cboLJ5zbbepAVUEGxqI"></script>

    <!-- Vendors -->
    <script src="/assets/vendor/tiny-slider/tiny-slider.js?v=TUoRPxlDCbgCqz7qhneDUfttB5a74WlyYrm3FOjFZnU"></script>
    <script src="/assets/vendor/purecounterjs/dist/purecounter_vanilla.js?v=faMnyiv1xv8mqVhNEZAf8nONW6FTf7T0o40mEeleNsQ"></script>
    <script src="/assets/vendor/glightbox/js/glightbox.js?v=r0f52UVK7WXq6iGxjyhjV-Y2WjDtpwawIrQaoBtwqE4"></script>
    <script src="/assets/vendor/flatpickr/js/flatpickr.min.js?v=AkQap91tDcS4YyQaZY2VV34UhSCxu2bDEIgXXXuf5Hg"></script>
    <script src="/assets/vendor/choices/js/choices.min.js?v=GGLVnYaVIFDbccxWOhWJiXbdGgmWv7nDSer8VyCQSBk"></script>
    <script src="/assets/vendor/jarallax/jarallax.min.js?v=051mEhTXV1pINFHbbB6peqpAeo2lEkRMtXVe59eo8Xo"></script>
    <script src="/assets/vendor/jarallax/jarallax-video.min.js?v=hfysllUmwJ3on3niY5cibYd5iwOz1MZ-A5aKYxeD0fw"></script>

    <!-- ThemeFunctions -->
    <script src="/assets/js/functions.js?v=TbMKeJx-U07R1sXdRBqHpmGBu1Oh1AfWjFjDMY8fuOA"></script>
    <script src="/js/site.js?v=KZhE6R9IutBwAcH2dhNWj-BkFRm9WXFHt-59TQi7rAo"></script>

    <script>
        var iframeOpenUrl = "";
        var globalPopups = GetStack();
        var globalCallBack = null;
        var globalWindowSender = null;
        function OnLanguageChange(lng) {

            if (lng === 'en-US') {
                return false;
            }
            ShowLoader();
            $.ajax({
                type: "POST",
                url: "/DZA/account/ChangeLanguage?hdnLang=" +lng,
                success: function (response) {
                    HideLoader();
                    if (response != "" && response != null && response.success === true) {
                        window.location.href = window.location.href;
                    }
                },
                error: function (response) {
                    HideLoader();
                    alert("error");
                },
            });
              return false;
        }
        function OnLogout() {
            $('#logoutModal').modal('show');
        }
        function OnLogoutSubmit() {
            ShowLoader();
            $.ajax({
                type: "POST",
                url: "/DZA/account/logout",
                success: function (response) {
                    HideLoader();
                    if (response != "" && response != null && response.success === true) {
                        window.location.href = response.ru;
                    }
                },
                error: function (response) {
                    HideLoader();
                    alert(response.error);
                },
            });
            return false;
        }
    </script>
    
    <script src="/assets/vendor/kendo/js/kendo.all.min.js?v=PO_iEpMMumS6ezVxpHt1IpXsBBwz5RAz-jXCrshdYLM"></script>

</body>
</html>
